mapping_file = "/Users/<USER>/project/callme/callmeso.com映射关系.txt"
retrofit_file = "/Users/<USER>/project/callme/app/src/main/java/com/score/callmetest/network/RetrofitUtils.kt"

# 读取映射文件
mappings = {}
with open(mapping_file, 'r') as f:
    for line in f:
        if '->' in line:
            server_path, local_path = line.strip().split(' -> ')
            mappings[local_path] = server_path

# 读取Retrofit文件中的现有映射
existing_mappings = set()
in_map_block = False
with open(retrofit_file, 'r') as f:
    for line in f:
        if 'private val urlMappingMap = mapOf(' in line:
            in_map_block = True
            continue
        if in_map_block and line.strip() == ')':
            in_map_block = False
            continue
        if in_map_block and ' to ' in line:
            parts = line.strip().split(' to ')
            if len(parts) >= 2:
                local_path = parts[0].strip().strip('"')
                existing_mappings.add(local_path)

# 找出缺失的映射
missing_mappings = {}
for local_path, server_path in mappings.items():
    if local_path not in existing_mappings:
        missing_mappings[local_path] = server_path

# 输出缺失的映射
print("Missing mappings:")
for local_path, server_path in missing_mappings.items():
    print(f'        "{local_path}" to "{server_path}",')

print(f"\nTotal missing mappings: {len(missing_mappings)}")
