package com.score.callmetest.db.repository

import com.score.callmetest.db.dao.CallHistoryDao
import com.score.callmetest.db.entity.CallHistoryRoomEntity
import com.score.callmetest.entity.CallHistoryEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * 通话历史仓库，负责处理通话历史相关的数据操作
 */
class CallHistoryRepository(private val callHistoryDao: CallHistoryDao) {
    /**
     * 插入一条通话历史
     * @param callHistory 通话历史实体
     */
    suspend fun insertCallHistory(callHistory: CallHistoryEntity) = withContext(Dispatchers.IO) {
        callHistoryDao.insertCallHistory(CallHistoryRoomEntity.fromEntity(callHistory))
    }
    
    /**
     * 批量插入通话历史
     * @param callHistories 通话历史实体列表
     */
    suspend fun insertCallHistories(callHistories: List<CallHistoryEntity>) = withContext(Dispatchers.IO) {
        callHistoryDao.insertCallHistories(callHistories.map { CallHistoryRoomEntity.fromEntity(it) })
    }
    
    /**
     * 更新一条通话历史
     * @param callHistory 通话历史实体
     */
    suspend fun updateCallHistory(callHistory: CallHistoryEntity) = withContext(Dispatchers.IO) {
        callHistoryDao.updateCallHistory(CallHistoryRoomEntity.fromEntity(callHistory))
    }
    
    /**
     * 删除一条通话历史
     * @param callHistory 通话历史实体
     */
    suspend fun deleteCallHistory(callHistory: CallHistoryEntity) = withContext(Dispatchers.IO) {
        callHistoryDao.deleteCallHistory(CallHistoryRoomEntity.fromEntity(callHistory))
    }
    
    /**
     * 根据ID删除一条通话历史
     * @param id 通话历史ID
     */
    suspend fun deleteCallHistoryById(id: String) = withContext(Dispatchers.IO) {
        callHistoryDao.deleteCallHistoryById(id)
    }
    
    /**
     * 获取所有通话历史，按时间戳倒序排序
     * @return 通话历史流
     */
    fun getAllCallHistories(): Flow<List<CallHistoryEntity>> {
        return callHistoryDao.getAllCallHistories().map { roomEntities ->
            roomEntities.map { it.toEntity() }
        }
    }
    
    /**
     * 获取特定用户的通话历史，按时间戳倒序排序
     * @param userId 用户ID
     * @return 通话历史流
     */
    fun getCallHistoriesByUserId(userId: String): Flow<List<CallHistoryEntity>> {
        return callHistoryDao.getCallHistoriesByUserId(userId).map { roomEntities ->
            roomEntities.map { it.toEntity() }
        }
    }
    
    /**
     * 根据ID获取一条通话历史
     * @param id 通话历史ID
     * @return 通话历史实体
     */
    suspend fun getCallHistoryById(id: String): CallHistoryEntity? = withContext(Dispatchers.IO) {
        callHistoryDao.getCallHistoryById(id)?.toEntity()
    }
    
    /**
     * 清空所有通话历史
     */
    suspend fun clearAllCallHistories() = withContext(Dispatchers.IO) {
        callHistoryDao.clearAllCallHistories()
    }
} 