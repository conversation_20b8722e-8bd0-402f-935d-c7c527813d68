package com.score.callmetest.db

import android.content.Context
import com.score.callmetest.db.repository.CallHistoryRepository
import com.score.callmetest.db.repository.MessageListRepository
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageListEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 数据库管理器，提供统一的数据库操作接口
 * 该类作为数据库操作的主入口，方便后续替换Room
 */
class DatabaseManager private constructor(context: Context) : DatabaseContract {
    
    // 协程作用域，用于执行数据库操作
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 数据库实例
    private val database = AppDatabase.getInstance(context)
    
    // 仓库实例
    private val messageListRepository = MessageListRepository(database.messageListDao())
    private val callHistoryRepository = CallHistoryRepository(database.callHistoryDao())
    
    companion object {
        @Volatile
        private var INSTANCE: DatabaseManager? = null
        
        /**
         * 获取数据库管理器实例
         * @param context 上下文
         * @return 数据库管理器实例
         */
        fun getInstance(context: Context): DatabaseManager {
            return INSTANCE ?: synchronized(this) {
                val instance = DatabaseManager(context)
                INSTANCE = instance
                instance
            }
        }
    }

    // <editor-folder desc="消息列表相关操作">

    /**
     * 插入一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.insertMessageList(messageList)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert message list")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 批量插入消息列表项
     * @param messageLists 消息列表项实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertMessageLists(messageLists: List<MessageListEntity>, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.insertMessageLists(messageLists)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert message lists")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 更新一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.updateMessageList(messageList)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update message list")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 删除一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.deleteMessageList(messageList)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete message list")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 根据用户ID删除一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteMessageListById(userId: String, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.deleteMessageListById(userId)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete message list by ID")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 获取所有消息列表项，按是否置顶和时间排序
     * @return 消息列表项流
     */
    override fun getAllMessageLists(): Flow<List<MessageListEntity>> {
        return messageListRepository.getAllMessageLists()
    }
    
    /**
     * 根据用户ID获取一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为消息列表项实体或null
     */
    override fun getMessageListById(userId: String, callback: (MessageListEntity?) -> Unit) {
        scope.launch {
            try {
                val messageList = messageListRepository.getMessageListById(userId)
                callback(messageList)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get message list by ID")
                callback(null)
            }
        }
    }
    
    /**
     * 更新未读消息数
     * @param userId 用户ID
     * @param unreadCount 未读消息数
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateUnreadCount(userId: String, unreadCount: Int, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.updateUnreadCount(userId, unreadCount)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update unread count")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 更新是否置顶
     * @param userId 用户ID
     * @param isPinned 是否置顶
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updatePinStatus(userId: String, isPinned: Boolean, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.updatePinStatus(userId, isPinned)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update pin status")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 清空所有消息列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun clearAllMessageLists(callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.clearAllMessageLists()
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear all message lists")
                callback?.invoke(false)
            }
        }
    }

    // </editor-folder>


    // <editor-folder desc="通话历史相关操作">

    /**
     * 插入一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.insertCallHistory(callHistory)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert call history")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 批量插入通话历史
     * @param callHistories 通话历史实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertCallHistories(callHistories: List<CallHistoryEntity>, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.insertCallHistories(callHistories)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert call histories")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 更新一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.updateCallHistory(callHistory)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update call history")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 删除一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.deleteCallHistory(callHistory)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete call history")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 根据ID删除一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteCallHistoryById(id: String, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.deleteCallHistoryById(id)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete call history by ID")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 获取所有通话历史，按时间戳倒序排序
     * @return 通话历史流
     */
    override fun getAllCallHistories(): Flow<List<CallHistoryEntity>> {
        return callHistoryRepository.getAllCallHistories()
    }
    
    /**
     * 获取特定用户的通话历史，按时间戳倒序排序
     * @param userId 用户ID
     * @return 通话历史流
     */
    override fun getCallHistoriesByUserId(userId: String): Flow<List<CallHistoryEntity>> {
        return callHistoryRepository.getCallHistoriesByUserId(userId)
    }
    
    /**
     * 根据ID获取一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为通话历史实体或null
     */
    override fun getCallHistoryById(id: String, callback: (CallHistoryEntity?) -> Unit) {
        scope.launch {
            try {
                val callHistory = callHistoryRepository.getCallHistoryById(id)
                callback(callHistory)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get call history by ID")
                callback(null)
            }
        }
    }
    
    /**
     * 清空所有通话历史
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun clearAllCallHistories(callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.clearAllCallHistories()
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear all call histories")
                callback?.invoke(false)
            }
        }
    }

    // </editor-folder>
} 