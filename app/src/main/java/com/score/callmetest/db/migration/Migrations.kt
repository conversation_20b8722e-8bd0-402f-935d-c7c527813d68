package com.score.callmetest.db.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import timber.log.Timber

/**
 * 数据库迁移类
 * 包含从各个版本升级到更高版本的迁移路径
 */
object Migrations {
    /**
     * 当前数据库版本
     * 在修改数据库结构时需要同步更新这个值和AppDatabase中的version值
     */
    const val CURRENT_VERSION = 1

    /**
     * 迁移映射表
     * 首次安装应用时不需要迁移，所以这里保持为空数组
     * 当未来需要升级数据库版本时，只需添加相应的迁移对象
     */
    val ALL_MIGRATIONS = arrayOf<Migration>(
        // 未来需要添加迁移时，取消下面的注释并实现相应的迁移逻辑
        // MIGRATION_1_TO_LATEST
    )

    /**
     * 从版本1直接迁移到最新版本的迁移对象
     * 这种方式避免了连续多次迁移，提高了效率
     * 注意：需要更新startVersion为1，endVersion为最新版本号
     */
    val MIGRATION_1_TO_LATEST = object : Migration(1, CURRENT_VERSION) {
        override fun migrate(database: SupportSQLiteDatabase) {
            Timber.d("Migrating database from version 1 to $CURRENT_VERSION")

            // 当版本升级时，在这里添加所有需要的迁移SQL语句
            // 例如:
            // if (CURRENT_VERSION >= 2) {
            //     // 版本2的变更
            //     database.execSQL("ALTER TABLE chat_messages ADD COLUMN read_status INTEGER NOT NULL DEFAULT 0")
            // }
            //
            // if (CURRENT_VERSION >= 3) {
            //     // 版本3的变更
            //     database.execSQL("ALTER TABLE message_list ADD COLUMN priority INTEGER NOT NULL DEFAULT 0")
            // }

            Timber.d("Migration from version 1 to $CURRENT_VERSION completed")
        }
    }

    /**
     * 当数据库结构变化较大时，可以使用此方法创建特定版本之间的迁移
     * 例如，从版本2到版本3的复杂迁移
     */
    val MIGRATION_2_3 = object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            Timber.d("Migrating database from version 2 to 3")

            // 具体的迁移逻辑

            Timber.d("Migration from version 2 to 3 completed")
        }
    }
} 