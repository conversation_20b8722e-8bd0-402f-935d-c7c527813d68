package com.score.callmetest.network

import retrofit2.Retrofit
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.Constant
import com.score.callmetest.CallmeApplication
import android.util.Log
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.logging.HttpLoggingInterceptor
import okhttp3.Interceptor
import okio.Buffer
import org.json.JSONObject
import okhttp3.Request
import com.score.callmetest.util.AESUtils
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.util.HeaderUtils
import com.score.callmetest.util.logAsTag
import okhttp3.Headers
import okhttp3.Protocol
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import retrofit2.converter.gson.GsonConverterFactory

object RetrofitUtils {

    // URL映射表 - 将代码中的接口路径映射到实际的服务器接口路径
    // 根据callmeso.com映射关系.txt文件生成，已去除重复项并按功能分类
    private val urlMappingMap = mapOf(
        // 视频通话相关接口
        "/video-call/hangUp" to "/baseApi/resource/harddisk_average_json/del",
        "/video-call/match" to "/baseApi/v2/link_string/search",
        "/video-call/pickUp" to "/baseApi/resource/report_maximum_price/del",
        "/video-call/command/confirm" to "/baseApi/document_second_hash/list",
        "/video-call/meetDislike" to "/baseApi/v1/config_second_result/get",
        "/video-call/channel/create" to "/baseApi/resource/message_second_rank/add",
        "/video-call/channel/join" to "/baseApi/res/followlist_max_rank/upload",
        "/video-call/channel/log" to "/baseApi/v0/disk_min_group/download",
        "/video-call/channel/logPostV2" to "/baseApi/link_value/delete",
        "/video-call/channel/latelyRecord" to "/baseApi/res/diagram_average_item/upload",
        "/video-call/match/cancel" to "/baseApi/msg_second_json/upload",
        "/video-call/match/clean/history" to "/baseApi/v2/promotion_first_list/add",
        "/video-call/match/exit" to "/baseApi/res/user_min_array/download",
        "/video-call/match/unity" to "/baseApi/v2/document_average_value/set",
        "/video-call/matchPopup" to "/baseApi/resource/goods_min_value/download",
        "/video-call/match/soul" to "/baseApi/v3/promotion_second_key/get",
        "/video-call/match/soul/guide" to "/baseApi/harddisk_maximum_map/search",
        "/video-call/newMatch" to "/baseApi/v2/game_last_array/update",
        "/video-call/meetCallClick" to "/baseApi/v1/harddisk_min_json/search",
        "/video-call/meetList" to "/baseApi/v3/goods_min_map/add",
        "/video-call/auto-call-record/updateStatus" to "/baseApi/v2/link_last_array/download",
        "/video-call/agora/rtmToken" to "/baseApi/v0/document_top_group/post",
        "/video-call/zego/rtcToken" to "/baseApi/v0/network_max_string/post",
        "/video-call/zego/rtcTokenPostV2" to "/baseApi/resource/money_rank/set",
        "/video-call/duration" to "/baseApi/resource/disk_minimum_price/post",
        "/video-call/record/warn" to "/baseApi/res/user_average_key/delete",
        "/video-call/channel/uploadAbnormalData" to "/baseApi/v3/meminfo_minimum_value/del",
        "/video-call/broadcaster/onlinePostV2" to "/baseApi/v2/harddisk_last_list/add",
        "/video-call/broadcaster/income" to "/baseApi/res/message_first_value/search",
        "/video-call/broadcaster/settlementIncome" to "/baseApi/v2/msg_maximum_price/del",
        "/video-call/user/callResult" to "/baseApi/v3/config_average_list/update",
        "/video-call/clearRulePostV2" to "/baseApi/v2/report_key/add",

        // 视频通话V2相关接口
        "/videoCallV2/user/callResult" to "/baseApi/v2/network_average_string/upload",
        "/videoCallV2/hangUp" to "/baseApi/v0/mem_second_price/post",
        "/videoCallV2/match/exit" to "/baseApi/v1/meminfo_item/download",
        "/videoCallV2/popUp" to "/baseApi/v0/game_max_string/upload",
        "/videoCallV2/channel/create" to "/baseApi/stock_average_table/patch",
        "/videoCallV2/duration" to "/baseApi/v1/report_max_group/del",

        // Soul视频通话相关接口
        "/soulVideoCall/v2/pickUp" to "/baseApi/v1/message_max_list/update",
        "/soulVideoCall/v2/channel/create" to "/baseApi/v1/order_first_array/set",
        "/soulVideoCall/v2/hangUp" to "/baseApi/v2/message_first_json/download",
        "/soulVideoCall/v2/popUp" to "/baseApi/v3/activity_average_item/post",
        "/soulVideoCall/broadcaster/match" to "/baseApi/v3/message_second_map/update",
        "/soulVideoCall/broadcaster/match/exit" to "/baseApi/v2/goods_maximum_hash/post",
        "/soulVideoCall/broadcaster/income" to "/baseApi/v0/user_second_result/patch",
        "/soulVideoCall/user/callResult" to "/baseApi/resource/memory_string/del",
        "/soulVideoCall/duration" to "/baseApi/v0/msg_minimum_item/update",
        // 用户相关接口
        "/user/getUserInfo" to "/baseApi/v3/game_last_array/list",
        "/user/getUserInfoPostV2" to "/baseApi/v1/stock_max_map/del",
        "/user/getUserInfoForH5" to "/baseApi/v3/money_value/update",
        "/user/getUserInfoForH5PostV2" to "/baseApi/v1/order_value/set",
        "/user/getUserInfoByUserNo" to "/baseApi/resource/activity_top_list/get",
        "/user/getUserInfoByUserNoPostV2" to "/baseApi/resource/device_average_array/del",
        "/user/getUserInfoForIndiaPkg" to "/baseApi/v3/document_max_json/patch",
        "/user/getUserInfoForIndiaPkgPostV2" to "/baseApi/resource/document_average_array/del",
        "/user/getUserInfoForWigo" to "/baseApi/v0/game_top_table/patch",
        "/user/getUserCoins" to "/baseApi/res/user_second_rank/patch",
        "/user/getUserCoinsPostV2" to "/baseApi/game_minimum_table/delete",
        "/user/saveUserInfo" to "/baseApi/msg_second_string/patch",
        "/user/saveUserInfoIncludeHobby" to "/baseApi/v0/coin_maximum_group/search",
        "/user/saveBasicInfo" to "/baseApi/v3/order_maximum_result/list",
        "/user/saveMoney" to "/baseApi/v1/order_average_list/set",
        "/user/saveMoney/list" to "/baseApi/res/message_json/update",
        "/user/saveMoney/statistic" to "/baseApi/v0/order_maximum_json/download",
        "/user/saveMoney/statisticPostV2" to "/baseApi/v3/disk_min_value/patch",
        "/user/saveMoney/del" to "/baseApi/v1/diagram_result/upload",
        "/user/getFriendsList" to "/baseApi/resource/link_average_json/upload",
        "/user/getFriendsListPostV2" to "/baseApi/v1/agent_max_item/delete",
        "/user/getFriendsListPage" to "/baseApi/v0/userinfo_minimum_price/upload",
        "/user/getAllFriendsList" to "/baseApi/v3/followlist_last_map/get",
        "/user/addFriend" to "/baseApi/promotion_last_list/upload",
        "/user/addFriendInReview" to "/baseApi/network_second_string/search",
        "/user/addFriendSoul" to "/baseApi/v2/followlist_result/list",
        "/user/acceptFriendApplyInReview" to "/baseApi/v2/activity_minimum_value/upload",
        "/user/rejectFriendApplyInReview" to "/baseApi/order_maximum_result/upload",
        "/user/delFriendInReview" to "/baseApi/v1/diagram_second_value/search",
        "/user/unfriend" to "/baseApi/v1/network_top_string/update",
        "/user/unfriendSoul" to "/baseApi/userinfo_top_array/set",
        "/user/followEachOther" to "/baseApi/v0/userinfo_result/delete",
        "/user/getIsFollowed" to "/baseApi/v0/goods_max_price/search",
        "/user/getIsFollowedPostV2" to "/baseApi/v1/user_minimum_price/upload",
        "/user/getIsFollowedInReview" to "/baseApi/v2/game_top_array/upload",
        "/user/getIsFriendPaid" to "/baseApi/res/activity_maximum_item/list",
        "/user/getUserOnlineStatus" to "/baseApi/resource/followlist_average_group/delete",
        "/user/getUserOnlineStatusPostV2" to "/baseApi/v1/doc_last_value/remove",
        "/user/getUserOnlineStatusForH5" to "/baseApi/v0/config_average_string/update",
        "/user/getUserOnlineStatusForH5PostV2" to "/baseApi/v1/network_value/remove",
        "/user/getUserListOnlineStatus" to "/baseApi/res/activity_max_result/remove",
        "/user/getUserListOnlineStatusPostV2" to "/baseApi/v3/config_first_item/set",
        "/user/getUserListOnlineStatusPostV3" to "/baseApi/v3/disk_first_rank/add",
        "/user/getUserListOnlineStatusJoinExtra" to "/baseApi/v1/user_maximum_list/add",
        "/user/getUserListOnlineStatusForH5" to "/baseApi/resource/disk_first_rank/add",
        "/user/getUserListOnlineStatusForH5PostV2" to "/baseApi/v1/network_value/remove",
        "/user/giveUserGifts" to "/baseApi/resource/coin_second_array/add",
        // 主播相关接口
        "/broadcaster/getBroadcasterMedias" to "/baseApi/v1/agent_max_key/upload",
        "/broadcaster/getBroadcasterPhotosAndVideos" to "/baseApi/stock_min_result/list",
        "/broadcaster/getBroadcasterFirstMedia" to "/baseApi/money_top_item/update",
        "/broadcaster/getBroadcasterExtraInfo" to "/baseApi/agent_minimum_result/get",
        "/broadcaster/getBroadcasterExtraInfoPostV2" to "/baseApi/v1/report_max_result/del",
        "/broadcaster/getBroadcasterExtraInfoForH5" to "/baseApi/v0/agent_first_json/upload",
        "/broadcaster/getBroadcasterExtraInfoForH5PostV2" to "/baseApi/product_maximum_map/search",
        "/broadcaster/getBroadcasterCoins" to "/baseApi/config_first_table/remove",
        "/broadcaster/getBroadcasterCoinsPostV2" to "/baseApi/v3/game_last_price/update",
        "/broadcaster/getBroadcasterStatistics" to "/baseApi/link_max_rank/download",
        "/broadcaster/search" to "/baseApi/doc_maximum_price/patch",
        "/broadcaster/searchV2" to "/baseApi/v1/mem_first_item/del",
        "/broadcaster/multiple/search" to "/baseApi/v1/userinfo_top_group/del",
        "/broadcaster/recommend/search" to "/baseApi/res/memory_top_value/del",
        "/broadcaster/recommend/sayHi" to "/baseApi/resource/gift_second_json/set",
        "/broadcaster/listRandomBroadcasterPostV3" to "/baseApi/v2/gift_minimum_string/del",
        "/broadcaster/getRandomBroadcaster" to "/baseApi/config_list/download",
        "/broadcaster/getRandomBroadcasterPostV2" to "/baseApi/v2/meminfo_last_map/set",
        "/broadcaster/visitUserDetail" to "/baseApi/document_average_price/list",
        "/broadcaster/broadcasterVisitors" to "/baseApi/v3/meminfo_max_group/get",
        "/broadcaster/broadcasterVisitorsPostV2" to "/baseApi/v3/goods_first_hash/set",
        "/broadcaster/broadcasterRelations" to "/baseApi/v1/harddisk_top_key/add",
        "/broadcaster/broadcasterRelationsPostV2" to "/baseApi/v2/goods_maximum_value/set",
        "/broadcaster/v2/broadcasterRelations" to "/baseApi/resource/goods_last_value/remove",
        "/broadcaster/broadcasterChangeMask" to "/baseApi/v3/game_max_key/patch",
        "/broadcaster/broadcasterMaskFindAndMark" to "/baseApi/v3/promotion_top_json/delete",
        "/broadcaster/guardianRank" to "/baseApi/resource/goods_min_item/update",
        "/broadcaster/guardian/search" to "/baseApi/v0/gift_minimum_group/download",
        "/broadcaster/wall/search" to "/baseApi/res/harddisk_rank/remove",
        "/broadcaster/wall/search/v2" to "/baseApi/v2/document_min_key/download",
        "/broadcaster/wall/searchForH5" to "/baseApi/res/message_price/remove",
        "/broadcaster/wall/searchForNearby" to "/baseApi/v3/user_max_string/del",
        "/broadcaster/wall/classifiedSearch" to "/baseApi/resource/network_last_map/upload",
        "/broadcaster/wall/broadcasterProvince" to "/baseApi/gift_average_map/delete",
        "/broadcaster/wall/lowPrice" to "/baseApi/res/harddisk_average_string/list",
        "/broadcaster/wall/highQuality" to "/baseApi/v2/meminfo_average_price/download",
        "/broadcaster/wall/comprehendLanguage" to "/baseApi/v0/gift_top_price/update",
        "/broadcaster/topGifters/search" to "/baseApi/v0/diagram_maximum_table/update",
        "/broadcaster/topGifters/searchPostV2" to "/baseApi/v2/disk_first_key/upload",
        "/broadcaster/rank/search" to "/baseApi/doc_maximum_price/patch",
        "/broadcaster/rank/searchV2" to "/baseApi/v1/mem_first_item/del",
        "/broadcaster/rank/searchRankForH5" to "/baseApi/v0/meminfo_minimum_price/download",
        "/broadcaster/rank/searchCouple" to "/baseApi/res/config_max_json/delete",
        "/broadcaster/rank/searchCoupleV2" to "/baseApi/v2/user_average_json/post",
        "/broadcaster/rank/searchForH5" to "/baseApi/v2/message_second_json/list",
        // 配置相关接口
        "/config/getAppConfig" to "/baseApi/v3/message_minimum_result/download",
        "/config/getAppConfigPostV2" to "/baseApi/resource/network_top_hash/patch",
        "/config/getIOSConfig" to "/baseApi/v3/followlist_minimum_item/remove",
        "/config/getIOSConfigPostV2" to "/baseApi/res/meminfo_minimum_json/remove",
        "/config/getStrategy" to "/baseApi/res/doc_second_result/update",
        "/config/getStrategyPostV2" to "/baseApi/res/stock_min_result/del",
        "/config/getStrategyForH5" to "/baseApi/device_top_group/update",
        "/config/getStrategyForH5PostV2" to "/baseApi/resource/game_top_string/update",
        "/config/getUserAuth" to "/baseApi/resource/game_minimum_map/update",
        "/config/getLangValue" to "/baseApi/v0/order_min_hash/search",
        "/config/getDefaultAvatarInfo" to "/baseApi/v1/coin_top_price/update",
        "/config/getDefaultAvatarInfoPostV2" to "/baseApi/v3/gift_max_map/post",
        "/config/getLaunchScreen" to "/baseApi/v2/meminfo_top_hash/post",
        "/config/getGuideInConfig" to "/baseApi/v2/userorder_min_table/del",
        "/config/getBeautyData" to "/baseApi/v3/link_top_table/search",
        "/config/getBeautyDataPostV2" to "/baseApi/v1/memory_first_hash/add",
        "/config/sys/notice" to "/baseApi/v1/doc_maximum_price/search",
        "/config/sys/noticePostV2" to "/baseApi/v2/agent_last_json/del",
        "/config/switch" to "/baseApi/res/gift_first_item/search",
        "/config/switchPostV2" to "/baseApi/v3/user_max_rank/patch",
        "/config/content/search" to "/baseApi/res/config_minimum_value/update",
        "/config/activity/slideshow" to "/baseApi/userorder_min_hash/del",
        "/config/activity/slideshowPostV2" to "/baseApi/v0/goods_second_rank/search",
        "/config/saveUserScreenshots" to "/baseApi/v0/activity_second_value/update",
        "/config/submitInstallReferer" to "/baseApi/v0/report_second_list/remove",
        "/config/submitInitData" to "/baseApi/v0/followlist_average_list/patch",
        "/config/sendDelAccount" to "/baseApi/goods_min_map/del",
        "/config/sendDelAccountPostV2" to "/baseApi/res/msg_second_value/add",
        "/config/sh" to "/baseApi/v3/goods_first_json/patch",
        // 安全相关接口
        "/security/login" to "/baseApi/v1/mem_maximum_value/update",
        "/security/phoneLogin" to "/baseApi/v1/mem_maximum_value/update",
        "/security/otpLogin" to "/baseApi/game_first_string/download",
        "/security/register" to "/baseApi/v2/money_second_value/set",
        "/security/emailRegister" to "/baseApi/v0/link_last_price/search",
        "/security/logout" to "/baseApi/v2/config_list/get",
        "/security/getOTP" to "/baseApi/v0/userorder_last_key/download",
        "/security/getOTPPostV2" to "/baseApi/res/coin_average_string/search",
        "/security/cleanOTP" to "/baseApi/res/product_min_group/delete",
        "/security/cleanOTPPostV2" to "/baseApi/res/doc_maximum_string/set",
        "/security/existsByMobile" to "/baseApi/v3/userinfo_average_json/post",
        "/security/existsByMobilePostV2" to "/baseApi/network_maximum_table/del",
        "/security/existsByEmail" to "/baseApi/v3/coin_average_table/add",
        "/security/existsByEmailPostV2" to "/baseApi/v1/device_min_json/del",
        "/security/exceptionCode" to "/baseApi/res/game_first_map/add",
        "/security/exceptionCodePostV2" to "/baseApi/res/doc_top_map/list",
        "/security/captchaApp" to "/baseApi/resource/coin_max_group/remove",
        "/security/oauth" to "/baseApi/userorder_second_group/download",
        "/security/isValidToken" to "/baseApi/userorder_minimum_item/upload",
        "/security/accountPassword" to "/baseApi/v1/goods_min_json/set",
        "/security/customCode" to "/baseApi/v2/disk_max_table/remove",
        "/security/time/proof" to "/baseApi/resource/order_rank/delete",
        "/security/selectCallActionLog" to "/baseApi/v0/activity_last_key/download",
        "/security/im/check" to "/baseApi/money_minimum_price/set",
        "/security/unBlock/pay" to "/baseApi/res/money_average_value/list",
        "/security/call/screenShotConfig" to "/baseApi/v3/product_last_group/add",
        "/security/call/screenShotUpload" to "/baseApi/res/money_second_map/del",
        "/security/oss/policy" to "/baseApi/res/user_average_rank/del",
        // 礼物相关接口
        "/gift/list" to "/baseApi/res/coin_max_key/del",
        "/gift/listPostV2" to "/baseApi/resource/order_minimum_price/delete",
        "/gift/v2/list" to "/baseApi/v3/link_first_item/upload",
        "/gift/v2/listPostV2" to "/baseApi/res/config_min_string/del",
        "/gift/live/list" to "/baseApi/res/link_first_key/get",
        "/gift/live/list/v2" to "/baseApi/link_first_key/delete",
        "/gift/live/list/v3" to "/baseApi/v0/product_second_string/delete",
        "/gift/live/list/get" to "/baseApi/res/money_average_string/patch",
        "/gift/live/list/getPostV2" to "/baseApi/v1/money_minimum_item/post",
        "/gift/live/small" to "/baseApi/resource/coin_max_item/upload",
        "/gift/specialList" to "/baseApi/v1/msg_first_map/get",
        "/gift/specialEffectsList" to "/baseApi/v2/product_first_price/update",
        "/gift/activity-only/showList" to "/baseApi/resource/document_maximum_string/set",
        "/gift/activity-only/showListPostV2" to "/baseApi/device_last_key/download",
        "/gift/activity-only/askList" to "/baseApi/v3/money_top_hash/delete",
        "/gift/activity-only/askListPostV2" to "/baseApi/res/link_second_array/list",
        "/gift/blindbox/rank" to "/baseApi/v2/game_min_list/post",
        "/gift/blindbox/rank/pop" to "/baseApi/v1/doc_max_result/search",
        "/gift/blindbox/award/record" to "/baseApi/res/game_first_group/post",
        "/gift/jackpot/award/info" to "/baseApi/resource/diagram_max_map/update",
        "/gift/jackpot/award/record" to "/baseApi/res/harddisk_second_map/get",
        "/gift/limitVIPGift/notPurchased" to "/baseApi/stock_last_price/upload",
        "/gift/limitVIPGift/login" to "/baseApi/v3/promotion_min_json/patch",
        "/gift/maskConfig" to "/baseApi/v1/harddisk_average_rank/list",
        "/gift/maskConfigPostV2" to "/baseApi/resource/message_last_key/upload",
        "/gift/broadcasterMask/unlock" to "/baseApi/resource/activity_top_price/add",
        "/gift/broadcasterMask/findLockInfo" to "/baseApi/gift_second_result/list",
        "/gift/askGiftPic/giftList" to "/baseApi/v2/order_min_json/post",
        "/gift/askGiftPic/giftListPostV2" to "/baseApi/resource/harddisk_first_hash/download",
        "/gift/giftCallGive" to "/baseApi/v1/stock_top_price/del",
        "/gift/favorite/switchTop" to "/baseApi/res/promotion_first_string/delete",
        "/gift/favorite/upsert" to "/baseApi/v1/followlist_maximum_array/patch",
        "/gift/favorite/delete" to "/baseApi/disk_last_map/upload",
        "/gift/v2/user-code" to "/baseApi/v2/promotion_min_item/add",
        "/gift/v2/user-codePostV2" to "/baseApi/v3/memory_minimum_string/search",
        "/gift/v2/user-code/batch" to "/baseApi/v3/link_minimum_list/add",
        "/gift/v2/user-code/batchPostV2" to "/baseApi/v3/link_minimum_list/add",
        "/gift/v2/user-codePostV2" to "/baseApi/v3/memory_minimum_string/search",
        "/gift/v2/user-code" to "/baseApi/v2/promotion_min_item/add",
        "/gift/favorite/delete" to "/baseApi/disk_last_map/upload",
        "/gift/favorite/upsert" to "/baseApi/v1/followlist_maximum_array/patch",
        "/gift/favorite/switchTop" to "/baseApi/res/promotion_first_string/delete",
        "/gift/giftCallGive" to "/baseApi/v1/stock_top_price/del",
        "/gift/askGiftPic/giftListPostV2" to "/baseApi/resource/harddisk_first_hash/download",
        "/gift/askGiftPic/giftList" to "/baseApi/v2/order_min_json/post",
        "/gift/broadcasterMask/findLockInfo" to "/baseApi/gift_second_result/list",
        "/gift/broadcasterMask/unlock" to "/baseApi/resource/activity_top_price/add",
        "/gift/maskConfigPostV2" to "/baseApi/resource/message_last_key/upload",
        "/gift/maskConfig" to "/baseApi/v1/harddisk_average_rank/list",
        "/gift/limitVIPGift/login" to "/baseApi/v3/promotion_min_json/patch",
        "/gift/limitVIPGift/notPurchased" to "/baseApi/stock_last_price/upload",
        "/gift/jackpot/award/record" to "/baseApi/res/harddisk_second_map/get",
        "/gift/jackpot/award/info" to "/baseApi/resource/diagram_max_map/update",
        "/gift/blindbox/award/record" to "/baseApi/res/game_first_group/post",
        "/gift/blindbox/rank/pop" to "/baseApi/v1/doc_max_result/search",
        "/gift/blindbox/rank" to "/baseApi/v2/game_min_list/post",
        "/gift/activity-only/askListPostV2" to "/baseApi/res/link_second_array/list",
        "/gift/activity-only/askList" to "/baseApi/v3/money_top_hash/delete",
        "/gift/activity-only/showListPostV2" to "/baseApi/device_last_key/download",
        "/gift/activity-only/showList" to "/baseApi/resource/document_maximum_string/set",
        "/gift/specialEffectsList" to "/baseApi/v2/product_first_price/update",
        "/gift/specialList" to "/baseApi/v1/msg_first_map/get",
        "/gift/giftMap/listPostV2" to "/baseApi/v0/doc_minimum_map/post",
        "/gift/giftMap/list" to "/baseApi/res/config_maximum_list/post",
        "/gift/live/small" to "/baseApi/resource/coin_max_item/upload",
        "/gift/live/list/getPostV2" to "/baseApi/v1/money_minimum_item/post",
        "/gift/live/list/get" to "/baseApi/res/money_average_string/patch",
        "/gift/live/list/v3" to "/baseApi/v0/product_second_string/delete",
        "/gift/live/list/v2" to "/baseApi/link_first_key/delete",
        "/gift/live/list" to "/baseApi/res/link_first_key/get",
        "/gift/v2/list" to "/baseApi/v3/link_first_item/upload",
        "/gift/listPostV2" to "/baseApi/resource/order_minimum_price/delete",
        "/gift/list" to "/baseApi/res/coin_max_key/del",
        // 金币相关接口
        "/coin/recharge/list" to "/baseApi/coin_last_value/list",
        "/coin/recharge/search" to "/baseApi/v2/userorder_maximum_key/upload",
        "/coin/recharge/create" to "/baseApi/config_average_value/download",
        "/coin/recharge/upgradeGoods" to "/baseApi/v1/meminfo_first_map/del",
        "/coin/recharge/broadcasterInvitation" to "/baseApi/v2/money_max_group/search",
        "/coin/recharge/broadcasterInvitationPostV2" to "/baseApi/v1/network_top_string/update",
        "/coin/recharge/checkBroadcasterInvitation" to "/baseApi/v3/agent_min_item/remove",
        "/coin/recharge/checkBroadcasterInvitationPostV2" to "/baseApi/resource/gift_maximum_group/upload",
        "/coin/recharge/getCoinLinkInfo" to "/baseApi/v0/mem_average_array/search",
        "/coin/recharge/payment/gp" to "/baseApi/res/link_array/upload",
        "/coin/recharge/payment/ipa" to "/baseApi/document_list/update",
        "/coin/recharge/subscribeVipSign" to "/baseApi/v3/network_first_list/patch",
        "/coin/goods/list" to "/baseApi/device_min_key/update",
        "/coin/goods/listPostV2" to "/baseApi/v1/mem_maximum_rank/download",
        "/coin/goods/search" to "/baseApi/link_minimum_string/remove",
        "/coin/goods/searchOverMaxGear" to "/baseApi/v3/message_minimum_price/list",
        "/coin/goods/broadcasterInvitation" to "/baseApi/v1/mem_minimum_value/delete",
        "/coin/goods/getPromotion" to "/baseApi/goods_maximum_json/delete",
        "/coin/goods/getLastSpecialOffer" to "/baseApi/resource/diagram_max_array/download",
        "/coin/goods/getLastSpecialOfferV2" to "/baseApi/resource/agent_last_rank/download",
        "/coin/goods/get" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getPostV2" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV2" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV3" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV4" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV5" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV6" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV7" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV8" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV9" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV10" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV11" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV12" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV13" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV14" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV15" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV16" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV17" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV18" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV19" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV20" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV21" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV22" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV23" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV24" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV25" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV26" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV27" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV28" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV29" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV30" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV31" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV32" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV33" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV34" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV35" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV36" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV37" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV38" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV39" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV40" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV41" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV42" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV43" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV44" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV45" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV46" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV47" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV48" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV49" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV50" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV51" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV52" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV53" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV54" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV55" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV56" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV57" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV58" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV59" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV60" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV61" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV62" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV63" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV64" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV65" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV66" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV67" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV68" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV69" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV70" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV71" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV72" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV73" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV74" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV75" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV76" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV77" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV78" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV79" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV80" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV81" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV82" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV83" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV84" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV85" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV86" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV87" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV88" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV89" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV90" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV91" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV92" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV93" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV94" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV95" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV96" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV97" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV98" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV99" to "/baseApi/resource/order_last_hash/post",
        "/coin/goods/getForH5PostV100" to "/baseApi/resource/order_last_hash/post"
    )

    // 生成32位密钥
    private fun generateKey(): String {
        val baseKey = Constant.getBaseUrl()
        return if (baseKey.length >= 32) {
            baseKey.substring(0, 32)
        } else {
            baseKey + "0".repeat(32 - baseKey.length)
        }
    }

    // 根据请求URL获取对应的加密密钥
    private fun getEncryptKey(request: Request): String {
        val urlPath = request.url.encodedPath
        return if (urlPath.endsWith("/config/getAppConfigPostV2") || urlPath.endsWith("baseApi/resource/network_top_hash/patch")) {
            // getAppConfigPostV2接口使用generateKey()
            generateKey()
        } else {
            // 其他接口优先从DecryptedAppConfig获取encrypt_key，没有再从SharedPreference获取
            val configEncryptKey = AppConfigManager.getEncryptKey()
            if (!configEncryptKey.isNullOrEmpty()) {
                configEncryptKey
            } else {
                SharePreferenceUtil.getString(Constant.ENCRYPT_KEY, "") ?: generateKey()
            }
        }
    }

    private val okHttpClient: OkHttpClient by lazy {
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true) // 启用自动重试
            .addInterceptor(NetworkCheckInterceptor()) // 网络检查
            .addInterceptor(RetryInterceptor()) // 重试
            .addInterceptor(UrlMappingInterceptor()) // URL映射
            .addInterceptor { chain ->
                val context = CallmeApplication.context
                val original = chain.request()
                val builder = original.newBuilder()

                // 使用HeaderUtils统一构建header
                val headers = HeaderUtils.buildCommonHeaders(context)
                for ((key, value) in headers) {
                    builder.header(key, value)
                }

                val request = builder.build()
                if (request.method == "POST") {
                    val newRequest = addHeadersToBody(request)
                    chain.proceed(newRequest)
                } else {
                    chain.proceed(request)
                }
            }
            .addInterceptor(CurlLoggingInterceptor())
            .addInterceptor(DecryptResponseInterceptor())
//            .addInterceptor(logging)
            .build()
    }

    private fun addHeadersToBody(request: Request): Request {
        val originalBody = request.body

        // 将headers转换为字符串
        val headersMap = mutableMapOf<String, String>()
        request.headers.forEach { (name, value) ->
            headersMap[name] = value.toString()
        }
        val headersString = JSONObject(headersMap).toString().replace("\\/", "/")

        // 准备要加密的JSON字符串
        val jsonToEncrypt = if (originalBody == null) {
            // 如果原始请求体为空，创建新的JSON对象
            JSONObject().apply {
                put("http_headers", headersString)
            }.toString().replace("\\/", "/")
        } else {
            // 读取原始请求体
            val buffer = Buffer()
            originalBody.writeTo(buffer)
            val contentType = originalBody.contentType()
            val charset = contentType?.charset(Charsets.UTF_8) ?: Charsets.UTF_8
            val bodyString = buffer.readString(charset)

            if (bodyString.isNullOrEmpty()) {
                // 如果原始请求体为空，创建新的JSON对象
                JSONObject().apply {
                    put("http_headers", headersString)
                }.toString().replace("\\/", "/")
            } else {
                try {
                    // 解析原始请求体
                    val bodyJson = JSONObject(bodyString)

                    // 添加 headers
                    bodyJson.put(
                        "http_headers",
                        JSONObject(headersString).toString().replace("\\/", "/")
                    )
                    bodyJson.toString().replace("\\/", "/")
                } catch (e: Exception) {
                    // 如果原始请求体不是JSON格式，返回原始请求
                    return request
                }
            }
        }

        // 使用AES加密
        try {
            val encryptKey = getEncryptKey(request)
            ("${request.url}, 加密key: " + encryptKey + ", 加密前：" + jsonToEncrypt).logAsTag(
                javaClass.name
            )

            val encryptedData = AESUtils.encrypt(jsonToEncrypt, encryptKey)

//            ("${request.url}, 加密key: " + encryptKey + ", 加密后：" + encryptedData).logAsTag(javaClass.name)

            // 直接将加密后的数据作为原始请求体，并移除所有headers
            val newBody = encryptedData.toRequestBody("application/json".toMediaType())

            return request.newBuilder()
                .method(request.method, newBody)
                .headers(Headers.Builder().build()) // 清空所有headers
                .header("content-type", "application/json")
                .build()
        } catch (e: Exception) {
            // 如果加密失败，返回原始请求
            return request
        }
    }

    val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl("https://" + Constant.getBaseUrl())
            .client(okHttpClient)
//            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    val apiService: ApiService by lazy {
        retrofit.create(ApiService::class.java)
    }
    
    // 日志上报专用的Retrofit实例，使用BASE_LOG_URL
    val logRetrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl("https://" + Constant.getLogUrl())
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    val logApiService: ApiService by lazy {
        logRetrofit.create(ApiService::class.java)
    }

    val dataRepository: DataRepository by lazy {
        DataRepository(apiService,logApiService)
    }

    // Curl 日志拦截器
    class CurlLoggingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val curlCmd = StringBuilder()
            curlCmd.append("curl -X ").append(request.method)
            curlCmd.append(" '").append(request.url).append("'")
            for (name in request.headers.names()) {
                val value = request.header(name)
                curlCmd.append(" -H '").append(name).append(": ").append(value).append("'")
            }
            val requestBody = request.body
            if (requestBody != null) {
                val buffer = Buffer()
                requestBody.writeTo(buffer)
                val charset = requestBody.contentType()?.charset(Charsets.UTF_8) ?: Charsets.UTF_8
                val body = buffer.readString(charset)
                if (body.isNotEmpty()) {
                    curlCmd.append(" --data '").append(body.replace("'", "\\'")).append("'")
                }
            }
//            println("[CURL] $curlCmd")
            "[CURL] $curlCmd".logAsTag(javaClass.name, Log.WARN)
            return chain.proceed(request)
        }
    }

    // 响应体解密拦截器
    class DecryptResponseInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            try {
                var response = chain.proceed(request)

                val responseBody = response.body ?: return response
                val contentType = responseBody.contentType()
                val encryptedString = responseBody.string().replace("\r\n", "")

                response = try {
                    val decryptKey = getEncryptKey(request)
//                    ("${request.url}, 解密key: " + decryptKey + ", 解密前：" + encryptedString).logAsTag(
//                        javaClass.name
//                    )
                    val decryptedString = AESUtils.decrypt(encryptedString, decryptKey)
                    ("${request.url}, 解密key: " + decryptKey + ", 解密后：" + decryptedString).logAsTag(
                        javaClass.name
                    )

                    val newBody = decryptedString.toResponseBody(contentType)
                    response.newBuilder().body(newBody).build()
                } catch (e: Exception) {
                    Log.e(this.javaClass.name, "解密出错： ", e)
                    // 解密失败，返回原始 response
                    response
                }
                return response
            } catch (e: Exception) {
                // 捕获超时等异常，返回一个408的空response，避免崩溃
                return Response.Builder()
                    .request(request)
                    .protocol(Protocol.HTTP_1_1)
                    .code(408) // 408 Request Timeout
                    .message("timeout: ${e.javaClass.simpleName}")
                    .body("".toResponseBody(null))
                    .build()
            }
        }
    }

    // URL映射拦截器 - 将代码中的接口路径映射到实际的服务器接口路径
    class UrlMappingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            val originalUrl = originalRequest.url
            val originalPath = originalUrl.encodedPath
            
            // 查找映射关系
            val mappedPath = urlMappingMap[originalPath]
            
            if (mappedPath != null) {
                // 记录URL映射日志
                ("URL映射: $originalPath -> $mappedPath").logAsTag(javaClass.name, Log.INFO)
                
                // 构建新的URL
                val newUrl = originalUrl.newBuilder()
                    .encodedPath(mappedPath)
                    .build()
                
                // 构建新的请求
                val newRequest = originalRequest.newBuilder()
                    .url(newUrl)
                    .build()
                
                return chain.proceed(newRequest)
            }
            
            // 如果没有映射关系，使用原始请求
            return chain.proceed(originalRequest)
        }
    }
} 