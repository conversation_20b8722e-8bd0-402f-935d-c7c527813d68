package com.score.callmetest.manager

import com.score.callmetest.network.AddFriendRequest
import com.score.callmetest.network.FollowModel
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UnFriendRequest
import com.score.callmetest.network.UserFollowPageRequest
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 关注管理类
 * 负责管理用户的关注、粉丝、互关等相关功能
 */
object FollowManager {

    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 刷新事件
     */
     class FollowListRefreshEvent

    /**
     * 关注类型枚举
     */
    object FollowType {
        const val FRIENDS = 1      // 互关列表
        const val FOLLOWERS = 2    // 粉丝列表
        const val FOLLOWING = 3    // 关注列表
    }

    /**
     * 分页请求结果回调
     */
    interface FollowPageCallback {
        fun onSuccess(list: List<FollowModel>)
        fun onError(errorMsg: String)
        fun onLoading(isLoading: Boolean)
    }

    /**
     * 关注/取关操作回调
     */
    interface FollowActionCallback {
        fun onSuccess()
        fun onError(errorMsg: String)
    }

    /**
     * 加载关注列表（type=3）
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    fun loadFollowingList(
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        fetchUserFollowPage(
            type = FollowType.FOLLOWING,
            limit = limit,
            page = page,
            callback = callback
        )
    }

    /**
     * 加载粉丝列表（type=2）
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    fun loadFollowersList(
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        fetchUserFollowPage(
            type = FollowType.FOLLOWERS,
            limit = limit,
            page = page,
            callback = callback
        )
    }

    /**
     * 加载互关列表（type=1）
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    fun loadFriendsList(
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        fetchUserFollowPage(
            type = FollowType.FRIENDS,
            limit = limit,
            page = page,
            callback = callback
        )
    }

    /**
     * 关注用户（添加好友）
     * @param userId 用户ID
     * @param callback 操作结果回调
     */
    fun followUser(userId: String, callback: FollowActionCallback) {
        coroutineScope.launch {
            try {
                val request = AddFriendRequest(userId)
                val response = RetrofitUtils.dataRepository.addFriend(request)
                if (response is NetworkResult.Success && response.data == true) {
                    callback.onSuccess()
                    // 关注成功后，刷新用户信息来更新数量
                    refreshFollowNotify()
                } else {
                    callback.onError( "关注失败--$response")
                }
            } catch (e: Exception) {
                callback.onError(e.message ?: "网络异常")
            }
        }
    }

    /**
     * 取关用户（删除好友）
     * @param userId 用户ID
     * @param callback 操作结果回调
     */
    fun unfollowUser(userId: String, callback: FollowActionCallback) {
        coroutineScope.launch {
            try {
                val request = UnFriendRequest(userId)
                val response = RetrofitUtils.dataRepository.unfriend(request)
                if (response is NetworkResult.Success && response.data == true) {
                    callback.onSuccess()
                    // 取关成功后，刷新用户信息来更新数量
                    refreshFollowNotify()
                } else {
                    callback.onError( "取关失败--$response")
                }
            } catch (e: Exception) {
                callback.onError(e.message ?: "网络异常")
            }
        }
    }

    /**
     * 通用分页请求
     * @param type 关系类型 1-friends;2-followers;3-following
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    private fun fetchUserFollowPage(
        type: Int,
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        callback.onLoading(true)
        coroutineScope.launch {
            try {
                val request = UserFollowPageRequest(type = type, limit = limit, page = page)
                val response = RetrofitUtils.dataRepository.getUserFollowPage(request)
                if (response is NetworkResult.Success) {
                    val list = response.data ?: emptyList()
                    // 记录日志
                    list.toString().logAsTag("FollowManager_fetchUserFollowPage_type_${type}: ")
                    callback.onSuccess(list)
                } else {
                    callback.onError( "请求失败--$response")
                }
            } catch (e: Exception) {
                callback.onError(e.message ?: "网络异常")
            } finally {
                callback.onLoading(false)
            }
        }
    }

    /**
     * 简化版关注操作，返回Boolean结果
     * @param userId 用户ID
     * @return 是否成功
     */
    suspend fun followUserSync(userId: String): Boolean {
        return try {
            val request = AddFriendRequest(userId)
            val response = RetrofitUtils.dataRepository.addFriend(request)
            response is NetworkResult.Success && response.data == true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 简化版取关操作，返回Boolean结果
     * @param userId 用户ID
     * @return 是否成功
     */
    suspend fun unfollowUserSync(userId: String): Boolean {
        return try {
            val request = UnFriendRequest(userId)
            val response = RetrofitUtils.dataRepository.unfriend(request)
            response is NetworkResult.Success && response.data == true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 刷新用户信息并通知关注数量变化
     */
    private fun refreshFollowNotify() {
            // 发送事件通知
            EventBus.post(FollowListRefreshEvent())
    }

}
