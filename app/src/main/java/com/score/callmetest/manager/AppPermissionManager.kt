package com.score.callmetest.manager

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.R
import com.score.callmetest.util.PermissionUtils
import com.score.callmetest.util.PermissionUtils.PermissionCallback
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import timber.log.Timber

/**
 * 应用权限管理器
 * 负责处理应用首次启动时的权限获取弹窗，以及在特定场景下重新请求权限
 */
object AppPermissionManager {
    private const val TAG = "AppPermissionManager"

    // 权限相关常量
    private const val KEY_FIRST_LAUNCH = "key_first_launch"
    private const val KEY_PERMISSION_DIALOG_SHOWN = "key_permission_dialog_shown"
    private const val KEY_CAMERA_PERMISSION_DENIED = "key_camera_permission_denied"
    private const val KEY_MICROPHONE_PERMISSION_DENIED = "key_microphone_permission_denied"
    private const val KEY_STORAGE_PERMISSION_DENIED = "key_storage_permission_denied"
    private const val KEY_PERMISSION_CHECK_COUNT = "key_permission_check_count"
    private const val KEY_LAST_PERMISSION_CHECK_TIME = "key_last_permission_check_time"

    // 永久拒绝权限相关常量（用户选择"拒绝且不再询问"）
    private const val KEY_CAMERA_PERMISSION_PERMANENTLY_DENIED = "key_camera_permission_permanently_denied"
    private const val KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED = "key_microphone_permission_permanently_denied"
    private const val KEY_STORAGE_PERMISSION_PERMANENTLY_DENIED = "key_storage_permission_permanently_denied"

    // 权限定义
    private val CAMERA_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
    private val MICROPHONE_PERMISSIONS = arrayOf(Manifest.permission.RECORD_AUDIO)
    private val STORAGE_PERMISSIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
    } else {
        arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
    }

    private const val REQUEST_CODE_FIRST_LAUNCH = 1001
    private const val REQUEST_CODE_STORAGE = 1002
    private const val REQUEST_CODE_CAMERA_MICROPHONE = 1003
    private const val REQUEST_CODE_MICROPHONE = 1004

    /**
     * 检查是否是首次启动
     */
    fun isFirstLaunch(context: Context): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_FIRST_LAUNCH, true)
    }

    /**
     * 标记非首次启动
     */
    fun markNotFirstLaunch(context: Context) {
        SharePreferenceUtil.putBoolean(KEY_FIRST_LAUNCH, false)
    }

    /**
     * 检查是否已显示过权限弹窗
     */
    fun hasShownPermissionDialog(context: Context): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_PERMISSION_DIALOG_SHOWN, false)
    }

    /**
     * 标记已显示权限弹窗
     */
    fun markPermissionDialogShown(context: Context) {
        SharePreferenceUtil.putBoolean(KEY_PERMISSION_DIALOG_SHOWN, true)
    }

    /**
     * 检查是否需要显示首次启动权限弹窗
     */
    fun shouldShowFirstLaunchPermissionDialog(context: Context): Boolean {
        return false
        // 不显示
//        return isFirstLaunch(context) && !hasShownPermissionDialog(context)
    }

    /**
     * 显示首次启动权限弹窗
     */
    fun showFirstLaunchPermissionDialog(
        activity: AppCompatActivity,
        onGranted: () -> Unit = {},
        onDenied: () -> Unit = {}
    ) {
        if (!shouldShowFirstLaunchPermissionDialog(activity)) {
            onGranted()
            return
        }

        val dialog = AlertDialog.Builder(activity)
            .setTitle("📱 Permission Request")
            .setMessage(
                "To provide you with a better service experience, we need the following permissions:\n\n" +
                        "📷 Album Permission\n" +
                        "   For uploading and modifying avatars, making your profile more complete\n\n" +
                        "📹 Camera Permission\n" +
                        "   For video calls, face-to-face communication with friends\n\n" +
                        "🎤 Microphone Permission\n" +
                        "   For voice calls, ensuring clear call quality\n\n" +
                        "💡 Reminder: You can modify these permissions in settings at any time"
            )
            .setPositiveButton("✅ Grant Now") { _, _ ->
                markPermissionDialogShown(activity)
                requestAllPermissions(activity, onGranted, onDenied)
            }
            .setNegativeButton("⏰ Later") { _, _ ->
                markPermissionDialogShown(activity)
                onDenied()
            }
            .setCancelable(false)
            .create()

        dialog.show()
    }

    /**
     * 请求所有权限
     */
    private fun requestAllPermissions(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val allPermissions = CAMERA_PERMISSIONS + MICROPHONE_PERMISSIONS + STORAGE_PERMISSIONS
        val missingPermissions = PermissionUtils.checkPermissions(activity, allPermissions)

        if (missingPermissions.isEmpty()) {
            onGranted()
            return
        }

        PermissionUtils.requestPermissions(
            activity,
            missingPermissions.toTypedArray(),
            REQUEST_CODE_FIRST_LAUNCH,
            object : PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                      Timber.tag(TAG).d("First launch permissions granted successfully: $permissions")
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                    Timber.tag(TAG).d("First launch permissions denied: $deniedPermissions, permanently denied: $permanentlyDeniedPermissions")
                    saveDeniedPermissions(activity, deniedPermissions)
                    savePermanentlyDeniedPermissions(activity, permanentlyDeniedPermissions)
                    onDenied()
                }
            }
        )
    }

    /**
     * 保存被拒绝的权限
     */
    private fun saveDeniedPermissions(context: Context, deniedPermissions: List<String>) {
        deniedPermissions.forEach { permission ->
            when (permission) {
                Manifest.permission.CAMERA -> {
                    SharePreferenceUtil.putBoolean(KEY_CAMERA_PERMISSION_DENIED, true)
                }

                Manifest.permission.RECORD_AUDIO -> {
                    SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_DENIED, true)
                }

                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.READ_MEDIA_IMAGES -> {
                    SharePreferenceUtil.putBoolean(KEY_STORAGE_PERMISSION_DENIED, true)
                }
            }
        }
    }

    /**
     * 保存被永久拒绝的权限（用户选择了"拒绝且不再询问"）
     */
    private fun savePermanentlyDeniedPermissions(context: Context, permanentlyDeniedPermissions: List<String>) {
        permanentlyDeniedPermissions.forEach { permission ->
            when (permission) {
                Manifest.permission.CAMERA -> {
                    SharePreferenceUtil.putBoolean(KEY_CAMERA_PERMISSION_PERMANENTLY_DENIED, true)
                }

                Manifest.permission.RECORD_AUDIO -> {
                    SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, true)
                }

                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.READ_MEDIA_IMAGES -> {
                    SharePreferenceUtil.putBoolean(KEY_STORAGE_PERMISSION_PERMANENTLY_DENIED, true)
                }
            }
        }
    }

    /**
     * 检查相册权限（用于上传头像或修改头像时）
     */
    fun checkAndRequestStoragePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        if (PermissionUtils.hasAllPermissions(activity, STORAGE_PERMISSIONS)) {
            onGranted()
            return
        }

        // 检查是否被永久拒绝（用户选择了"拒绝且不再询问"）
        val storagePermanentlyDenied = SharePreferenceUtil.getBoolean(KEY_STORAGE_PERMISSION_PERMANENTLY_DENIED, false)

        if (storagePermanentlyDenied) {
            // 显示Toast并直接跳转到设置页
            ToastUtils.showShortToast(context.getString(R.string.storage_permission_check_hint))
            // 延迟1秒再跳转,避免不弹窗
            Handler(Looper.getMainLooper()).postDelayed({
                openAppSettings(activity)
            },1000)
            onDenied()
            return
        }

        // 直接请求权限，不显示说明对话框
        requestStoragePermission(activity, onGranted, onDenied)
    }

    /**
     * 检查摄像头和麦克风权限（用于拨打或接听电话时）
     */
    fun checkAndRequestCameraMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit = {},
        onDenied: () -> Unit = {}
    ) {
        val allPermissions = CAMERA_PERMISSIONS + MICROPHONE_PERMISSIONS
        if (PermissionUtils.hasAllPermissions(activity, allPermissions)) {
            onGranted()
            return
        }

        // 检查是否被永久拒绝（用户选择了"拒绝且不再询问"）
        val cameraPermanentlyDenied = SharePreferenceUtil.getBoolean(KEY_CAMERA_PERMISSION_PERMANENTLY_DENIED, false)
        val microphonePermanentlyDenied = SharePreferenceUtil.getBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, false)

        if (cameraPermanentlyDenied || microphonePermanentlyDenied) {
            // 显示Toast并直接跳转到设置页
            ToastUtils.showShortToast(context.getString(R.string.camera_microphone_permission_check_hint))
            // 延迟1秒再跳转,避免不弹窗
            Handler(Looper.getMainLooper()).postDelayed({
                openAppSettings(activity)
            },1000)
            onDenied()
            return
        }

        // 直接请求权限，不显示说明对话框
        requestCameraMicrophonePermission(activity, onGranted, onDenied)
    }

    /**
     * 检查麦克风权限（用于发送语音时）
     */
    fun checkAndRequestMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val allPermissions = MICROPHONE_PERMISSIONS
        if (PermissionUtils.hasAllPermissions(activity, allPermissions)) {
            onGranted()
            return
        }

        // 检查是否被永久拒绝（用户选择了"拒绝且不再询问"）
        val microphonePermanentlyDenied = SharePreferenceUtil.getBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, false)

        if (microphonePermanentlyDenied) {
            // 显示Toast并直接跳转到设置页
            ToastUtils.showShortToast(context.getString(R.string.microphone_permission_check_hint))
            // 延迟1秒再跳转,避免不弹窗
            Handler(Looper.getMainLooper()).postDelayed({
                openAppSettings(activity)
            },1000)
            onDenied()
            return
        }

        // 直接请求权限，不显示说明对话框
        requestMicrophonePermission(activity, onGranted, onDenied)
    }


    /**
     * 显示相册权限说明对话框
     */
    private fun showStoragePermissionRationaleDialog(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val dialog = AlertDialog.Builder(activity)
            .setTitle("Album Permission Required")
            .setMessage(
                "In order to function properly, APPNAME needs permission to take pictures and albums.\n\n" +
                        "Please go to settings to enable the album permission, then return to the app to continue."
            )
            .setPositiveButton("⚙️ Go to setting") { _, _ ->
                openAppSettings(activity)
                onDenied()
            }
            .setNegativeButton("❌ Cancel") { _, _ ->
                onDenied()
            }
            .setCancelable(false)
            .create()

        dialog.show()
    }

    /**
     * 显示摄像头和麦克风权限说明对话框
     */
    private fun showCameraMicrophonePermissionRationaleDialog(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val dialog = AlertDialog.Builder(activity)
            .setTitle("Camera & Microphone Permission Required")
            .setMessage(
                "Dear Sir, you didn't allow us to access your camera and microphone.\n" +
                "If you want to use our app function comprehensively, you better turn on your permissions of camera and microphone."
            )
            .setPositiveButton("⚙️ Go to setting") { _, _ ->
                openAppSettings(activity)
                onDenied()
            }
            .setNegativeButton("❌ Cancel") { _, _ ->
                onDenied()
            }
            .setCancelable(false)
            .create()
        dialog.show()
    }

    /**
     * 显示麦克风权限说明对话框
     */
    private fun showMicrophonePermissionRationaleDialog(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val dialog = AlertDialog.Builder(activity)
            .setTitle("Microphone Permission Required")
            .setMessage(
                "In order to function properly, APPNAME needs permission to access the Microphone.\n\n" +
                        "Please go to settings to enable the microphone permission, then return to the app to continue."
            )
            .setPositiveButton("⚙️ Go to setting") { _, _ ->
                openAppSettings(activity)
                onDenied()
            }
            .setNegativeButton("❌ Cancel") { _, _ ->
                onDenied()
            }
            .setCancelable(false)
            .create()
        dialog.show()
    }

    /**
     * 请求相册权限
     */
    private fun requestStoragePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        PermissionUtils.requestPermissions(
            activity,
            STORAGE_PERMISSIONS,
            REQUEST_CODE_STORAGE,
            object : PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                    Timber.tag(TAG).d("Storage permission granted successfully: $permissions")
                    SharePreferenceUtil.putBoolean(KEY_STORAGE_PERMISSION_DENIED, false)
                    SharePreferenceUtil.putBoolean(KEY_STORAGE_PERMISSION_PERMANENTLY_DENIED, false)
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                   Timber.tag(TAG).d("Storage permission denied: $deniedPermissions")
                    saveDeniedPermissions(activity, deniedPermissions)
                    savePermanentlyDeniedPermissions(activity, permanentlyDeniedPermissions)
                    onDenied()
                }
            }
        )
    }

    /**
     * 请求摄像头和麦克风权限
     */
    private fun requestCameraMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val allPermissions = CAMERA_PERMISSIONS + MICROPHONE_PERMISSIONS
        PermissionUtils.requestPermissions(
            activity,
            allPermissions,
            REQUEST_CODE_CAMERA_MICROPHONE,
            object : PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                   Timber.tag(TAG).d("Camera and microphone permissions granted successfully: $permissions")
                    SharePreferenceUtil.putBoolean(KEY_CAMERA_PERMISSION_DENIED, false)
                    SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_DENIED, false)
                    SharePreferenceUtil.putBoolean(KEY_CAMERA_PERMISSION_PERMANENTLY_DENIED, false)
                    SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, false)
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                   Timber.tag(TAG).d("Camera and microphone permissions denied: $deniedPermissions")
                    saveDeniedPermissions(activity, deniedPermissions)
                    savePermanentlyDeniedPermissions(activity, permanentlyDeniedPermissions)
                    onDenied()
                }
            }
        )
    }

    /**
     * 请求麦克风权限
     */
    private fun requestMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val allPermissions = MICROPHONE_PERMISSIONS
        PermissionUtils.requestPermissions(
            activity,
            allPermissions,
            REQUEST_CODE_MICROPHONE,
            object : PermissionUtils.PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                   Timber.tag(TAG).d("Microphone permission granted successfully: $permissions")
                    SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_DENIED, false)
                    SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, false)
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                   Timber.tag(TAG).d("Microphone permission denied: $deniedPermissions")
                    saveDeniedPermissions(activity, deniedPermissions)
                    savePermanentlyDeniedPermissions(activity, permanentlyDeniedPermissions)
                    onDenied()
                }
            }
        )
    }

    /**
     * 打开应用设置页面
     */
    private fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri = Uri.fromParts("package", context.packageName, null)
        intent.data = uri
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }

    /**
     * 处理权限请求结果
     */
    fun handleRequestPermissionsResult(
        activity: Activity,
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        PermissionUtils.handleRequestPermissionsResult(activity, requestCode, permissions, grantResults)
    }

    /**
     * 检查特定权限是否已授予
     */
    fun hasStoragePermission(context: Context): Boolean {
        return PermissionUtils.hasAllPermissions(context, STORAGE_PERMISSIONS)
    }

    fun hasCameraPermission(context: Context): Boolean {
        return PermissionUtils.hasAllPermissions(context, CAMERA_PERMISSIONS)
    }

    fun hasMicrophonePermission(context: Context): Boolean {
        return PermissionUtils.hasAllPermissions(context, MICROPHONE_PERMISSIONS)
    }

    fun hasCameraMicrophonePermission(context: Context): Boolean {
        val allPermissions = CAMERA_PERMISSIONS + MICROPHONE_PERMISSIONS
        return PermissionUtils.hasAllPermissions(context, allPermissions)
    }

    /**
     * 检查权限状态变化（在Activity的onResume中调用）
     */
    fun checkPermissionStatusChange(activity: AppCompatActivity) {
        val currentTime = System.currentTimeMillis()
        val lastCheckTime = SharePreferenceUtil.getLong(KEY_LAST_PERMISSION_CHECK_TIME, 0)

        // 避免频繁检查，至少间隔1秒
        if (currentTime - lastCheckTime < 1000) {
            return
        }

        SharePreferenceUtil.putLong(KEY_LAST_PERMISSION_CHECK_TIME, currentTime)

        // 检查是否有权限状态变化
        val storageWasDenied = SharePreferenceUtil.getBoolean(KEY_STORAGE_PERMISSION_DENIED, false)
        val cameraWasDenied = SharePreferenceUtil.getBoolean(KEY_CAMERA_PERMISSION_DENIED, false)
        val microphoneWasDenied =
            SharePreferenceUtil.getBoolean(KEY_MICROPHONE_PERMISSION_DENIED, false)

        val storageNowGranted = hasStoragePermission(activity)
        val cameraNowGranted = hasCameraPermission(activity)
        val microphoneNowGranted = hasMicrophonePermission(activity)

        // 如果之前被拒绝的权限现在被授予了，清除拒绝状态和永久拒绝状态
        if (storageWasDenied && storageNowGranted) {
            SharePreferenceUtil.putBoolean(KEY_STORAGE_PERMISSION_DENIED, false)
            SharePreferenceUtil.putBoolean(KEY_STORAGE_PERMISSION_PERMANENTLY_DENIED, false)
           Timber.tag(TAG).d( "相册权限状态变化：已授予")
        }

        if (cameraWasDenied && cameraNowGranted) {
            SharePreferenceUtil.putBoolean(KEY_CAMERA_PERMISSION_DENIED, false)
            SharePreferenceUtil.putBoolean(KEY_CAMERA_PERMISSION_PERMANENTLY_DENIED, false)
           Timber.tag(TAG).d( "摄像头权限状态变化：已授予")
        }

        if (microphoneWasDenied && microphoneNowGranted) {
            SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_DENIED, false)
            SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, false)
           Timber.tag(TAG).d("麦克风权限状态变化：已授予")
        }
    }

    /**
     * 获取权限状态摘要
     */
    fun getPermissionStatusSummary(context: Context): String {
        val storageGranted = hasStoragePermission(context)
        val cameraGranted = hasCameraPermission(context)
        val microphoneGranted = hasMicrophonePermission(context)

        return "Storage Permission: ${if (storageGranted) "✅" else "❌"}, " +
                "Camera Permission: ${if (cameraGranted) "✅" else "❌"}, " +
                "Microphone Permission: ${if (microphoneGranted) "✅" else "❌"}"
    }

    /**
     * 检查是否需要显示权限引导
     */
    fun shouldShowPermissionGuide(context: Context): Boolean {
        val checkCount = SharePreferenceUtil.getInt(KEY_PERMISSION_CHECK_COUNT, 0)
        val hasAnyPermission = hasStoragePermission(context) ||
                hasCameraPermission(context) ||
                hasMicrophonePermission(context)

        // 如果没有任何权限且检查次数少于3次，显示引导
        return !hasAnyPermission && checkCount < 3
    }

    /**
     * 增加权限检查次数
     */
    fun incrementPermissionCheckCount() {
        val currentCount = SharePreferenceUtil.getInt(KEY_PERMISSION_CHECK_COUNT, 0)
        SharePreferenceUtil.putInt(KEY_PERMISSION_CHECK_COUNT, currentCount + 1)
    }

    /**
     * 重置权限检查次数（用于测试）
     */
    fun resetPermissionCheckCount() {
        SharePreferenceUtil.putInt(KEY_PERMISSION_CHECK_COUNT, 0)
    }

    /**
     * 检查摄像头权限是否被永久拒绝
     */
    fun isCameraPermissionPermanentlyDenied(): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_CAMERA_PERMISSION_PERMANENTLY_DENIED, false)
    }

    /**
     * 检查麦克风权限是否被永久拒绝
     */
    fun isMicrophonePermissionPermanentlyDenied(): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, false)
    }

    /**
     * 检查相册权限是否被永久拒绝
     */
    fun isStoragePermissionPermanentlyDenied(): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_STORAGE_PERMISSION_PERMANENTLY_DENIED, false)
    }

    /**
     * 重置所有永久拒绝状态（用于测试）
     */
    fun resetPermanentlyDeniedPermissions() {
        SharePreferenceUtil.putBoolean(KEY_CAMERA_PERMISSION_PERMANENTLY_DENIED, false)
        SharePreferenceUtil.putBoolean(KEY_MICROPHONE_PERMISSION_PERMANENTLY_DENIED, false)
        SharePreferenceUtil.putBoolean(KEY_STORAGE_PERMISSION_PERMANENTLY_DENIED, false)
    }

    /**
     * 请求悬浮窗权限（会自动跳转设置页，前置说明弹窗）
     */
    fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context)) {
            // 弹出说明指示框
            AlertDialog.Builder(context)
                .setTitle("Overlay Permission Required")
                .setMessage("To remind you promptly when receiving calls, we need to enable the 'Display over other apps' permission. Please allow this permission on the next page.")
                .setPositiveButton("Grant Permission") { _, _ ->
                    val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + context.packageName))
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                }
                .setNegativeButton("Cancel", null)
                .show()
            return
        }
    }
}