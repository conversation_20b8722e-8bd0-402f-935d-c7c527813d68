package com.score.callmetest.manager

import android.annotation.TargetApi
import android.app.Activity
import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.media.AudioAttributes
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.media.AudioManager.OnAudioFocusChangeListener
import android.media.MediaPlayer
import android.media.MediaPlayer.OnCompletionListener
import android.media.MediaPlayer.OnPreparedListener
import android.media.MediaPlayer.OnSeekCompleteListener
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.os.PowerManager.WakeLock
import android.util.Log
import android.view.WindowManager
import java.io.FileInputStream
import java.io.IOException


object AudioPlayManager : SensorEventListener {

    private const val TAG: String = "AudioPlayManager"

    private var mMediaPlayer: MediaPlayer? = null
    private var _playListener: IAudioPlayListener? = null
    private var mUriPlaying: Uri? = null
    private var _sensor: Sensor? = null
    private var _sensorManager: SensorManager? = null
    private var mAudioManager: AudioManager? = null
    private var _powerManager: PowerManager? = null
    private var _wakeLock: WakeLock? = null
    private var afChangeListener: OnAudioFocusChangeListener? = null
    private val handler: Handler = Handler(Looper.getMainLooper())
    private val mLock = Any()

    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
    override fun onSensorChanged(event: SensorEvent) {
        synchronized(mLock) {
            try {
                val range = event.values[0]
                Log.d(
                    TAG,
                    "onSensorChanged. range:"
                            + range
                            + " max range:"
                            + event.sensor.getMaximumRange())
                
                
                val rangeJudgeValue = 0.0
                val judge: Boolean
                if (_sensor == null || mMediaPlayer == null || mAudioManager == null) {
                    return
                }
                judge = judgeCondition(event, range, rangeJudgeValue)

                if (mMediaPlayer!!.isPlaying()) {
                    var fis: FileInputStream? = null
                    if (judge) {
                        // 处理 sensor 出现异常后，持续回调 sensor 变化，导致声音播放卡顿
                        if (mAudioManager!!.getMode() == AudioManager.MODE_NORMAL) return
                        mAudioManager!!.setMode(AudioManager.MODE_NORMAL)
                        mAudioManager!!.setSpeakerphoneOn(true)
                        val positions = mMediaPlayer!!.getCurrentPosition()
                        try {
                            mMediaPlayer!!.reset()
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                                val attributes =
                                    AudioAttributes.Builder()
                                        .setUsage(AudioAttributes.USAGE_MEDIA)
                                        .build()
                                mMediaPlayer!!.setAudioAttributes(attributes)
                            } else {
                                mMediaPlayer!!.setAudioStreamType(AudioManager.STREAM_MUSIC)
                            }
                            mMediaPlayer!!.setVolume(1f, 1f)
                            fis = FileInputStream(mUriPlaying!!.getPath())
                            mMediaPlayer!!.setDataSource(fis.getFD())
                            mMediaPlayer!!.setOnPreparedListener(
                                object : OnPreparedListener {
                                    override fun onPrepared(mp: MediaPlayer) {
                                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                            mp.seekTo(positions.toLong(), MediaPlayer.SEEK_CLOSEST)
                                        } else {
                                            mp.seekTo(positions)
                                        }
                                    }
                                })
                            mMediaPlayer!!.setOnSeekCompleteListener(
                                object : OnSeekCompleteListener {
                                    override fun onSeekComplete(mp: MediaPlayer) {
                                        mp.start()
                                    }
                                })
                            mMediaPlayer!!.prepareAsync()
                        } catch (e: IOException) {
                            Log.e(TAG, "onSensorChanged", e)
                        } finally {
                            if (fis != null) {
                                try {
                                    fis.close()
                                } catch (e: IOException) {
                                    Log.e(TAG, "startPlay", e)
                                }
                            }
                        }

                        setScreenOn()
                    } else {
                        if (!(Build.BRAND == "samsung" && Build.MODEL == "SM-N9200")) {
                            setScreenOff()
                        }
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                            if (mAudioManager!!.getMode() == AudioManager.MODE_IN_COMMUNICATION) return
                            mAudioManager!!.setMode(AudioManager.MODE_IN_COMMUNICATION)
                        } else {
                            if (mAudioManager!!.getMode() == AudioManager.MODE_IN_CALL) return
                            mAudioManager!!.setMode(AudioManager.MODE_IN_CALL)
                        }
                        mAudioManager!!.setSpeakerphoneOn(false)
                        replay()
                    }
                } else {
                    if (range > 0.0) {
                        if (mAudioManager!!.getMode() == AudioManager.MODE_NORMAL) return
                        mAudioManager!!.setMode(AudioManager.MODE_NORMAL)
                        mAudioManager!!.setSpeakerphoneOn(true)
                        setScreenOn()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "onSensorChanged", e)
            }
        }
    }

    private fun judgeCondition(event: SensorEvent, range: Float, rangeJudgeValue: Double): Boolean {
        var rangeJudgeValue = rangeJudgeValue
        synchronized(mLock) {
            val judge: Boolean
            if (Build.BRAND.equals("HUAWEI", ignoreCase = true)) {
                judge = (range >= event.sensor.getMaximumRange())
            } else {
                if (Build.BRAND.equals("ZTE", ignoreCase = true)) {
                    rangeJudgeValue = 1.0
                } else if (Build.BRAND.equals("nubia", ignoreCase = true)) {
                    rangeJudgeValue = 3.0
                }
                judge = (range > rangeJudgeValue)
            }
            return judge
        }
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private fun setScreenOff() {
        synchronized(mLock) {
            if (_wakeLock == null && _powerManager != null) {
                _wakeLock =
                    _powerManager!!.newWakeLock(
                        PowerManager.PROXIMITY_SCREEN_OFF_WAKE_LOCK,
                        "AudioPlayManager:wakelockTag"
                    )
            }
            if (_wakeLock != null && !_wakeLock!!.isHeld()) {
                _wakeLock!!.acquire(10 * 60 * 1000L /*10 minutes*/)
            }
        }
    }

    private fun setScreenOn() {
        synchronized(mLock) {
            if (_wakeLock != null && _wakeLock!!.isHeld()) {
                _wakeLock!!.setReferenceCounted(false)
                _wakeLock!!.release()
                _wakeLock = null
            }
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // do nothing
    }

    private fun replay() {
        synchronized(mLock) {
            if (mMediaPlayer == null) {
                return
            }
            var fis: FileInputStream? = null
            try {
                val positions = mMediaPlayer!!.getCurrentPosition()
                mMediaPlayer!!.reset()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    val attributes =
                        AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                            .setLegacyStreamType(AudioManager.STREAM_VOICE_CALL)
                            .build()
                    mMediaPlayer!!.setAudioAttributes(attributes)
                } else {
                    mMediaPlayer!!.setAudioStreamType(AudioManager.STREAM_VOICE_CALL)
                }
                fis = FileInputStream(mUriPlaying!!.getPath())
                mMediaPlayer!!.setDataSource(fis.getFD())
                mMediaPlayer!!.setOnPreparedListener(
                    object : OnPreparedListener {
                        override fun onPrepared(mp: MediaPlayer) {
                            // 装载完毕回调
                            try {
                                Thread.sleep(1000)
                            } catch (e: InterruptedException) {
                                Log.e(TAG, "replay", e)
                                // Restore interrupted state...
                                Thread.currentThread().interrupt()
                            }

                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                mp.seekTo(positions.toLong(), MediaPlayer.SEEK_CLOSEST)
                            } else {
                                mp.seekTo(positions)
                            }
                        }
                    })
                mMediaPlayer!!.setOnSeekCompleteListener(
                    object : OnSeekCompleteListener {
                        override fun onSeekComplete(mp: MediaPlayer) {
                            mp.start()
                        }
                    })
                // 通过异步的方式装载媒体资源
                mMediaPlayer!!.prepareAsync()
                mMediaPlayer!!.setVolume(1.0f, 1.0f)
            } catch (e: IOException) {
                Log.e(TAG, "replay", e)
            } finally {
                if (fis != null) {
                    try {
                        fis.close()
                    } catch (e: IOException) {
                        Log.e(TAG, "replay", e)
                    }
                }
            }
        }
    }

        fun startPlay(context: Context?, audioUri: Uri?, playListener: IAudioPlayListener?) {
        synchronized(mLock) {
            if (context == null || audioUri == null) {
                Log.e(TAG, "startPlay context or audioUri is null.")
                return
            }
            Log.e(TAG, "startPlay audioUri is $audioUri")

            _playListener?.onStop(mUriPlaying)
            resetMediaPlayer()

            this.afChangeListener =
                OnAudioFocusChangeListener { focusChange ->
                    synchronized(mLock) {
                        Log.d(TAG, "OnAudioFocusChangeListener " + focusChange)
                        if (mAudioManager != null
                            && focusChange == AudioManager.AUDIOFOCUS_LOSS
                        ) {
                            mAudioManager!!.abandonAudioFocus(afChangeListener)
                            afChangeListener = null
                            handler.post(
                                object : Runnable {
                                    override fun run() {
                                        synchronized(mLock) {
                                            _playListener?.onComplete(mUriPlaying)
                                            _playListener = null
                                        }
                                    }
                                })
                            reset()
                        }
                    }
                }

            var fis: FileInputStream? = null
            if (context is Activity) {
                context
                    .getWindow()
                    .addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
            try {
                _powerManager =
                    context.getApplicationContext()
                        .getSystemService(Context.POWER_SERVICE) as PowerManager?
                mAudioManager =
                    context.getApplicationContext()
                        .getSystemService(Context.AUDIO_SERVICE) as AudioManager?
                if (!isHeadphonesPlugged(mAudioManager)) {
                    _sensorManager =
                        context.getApplicationContext()
                            .getSystemService(Context.SENSOR_SERVICE) as SensorManager?
                    if (_sensorManager != null) {
                        _sensor = _sensorManager!!.getDefaultSensor(Sensor.TYPE_PROXIMITY)
                        _sensorManager!!.registerListener(
                            this, _sensor, SensorManager.SENSOR_DELAY_NORMAL
                        )
                    }
                }
                muteAudioFocus(mAudioManager, true)

                _playListener = playListener
                mUriPlaying = audioUri
                mMediaPlayer = MediaPlayer()
                mMediaPlayer!!.setOnCompletionListener(
                    object : OnCompletionListener {
                        override fun onCompletion(mp: MediaPlayer?) {
                            synchronized(mLock) {
                                _playListener?.onComplete(mUriPlaying)
                                _playListener = null
                                reset()
                                if (context is Activity) {
                                    context
                                        .getWindow()
                                        .clearFlags(
                                            WindowManager.LayoutParams
                                                .FLAG_KEEP_SCREEN_ON
                                        )
                                }
                            }
                        }
                    })
                mMediaPlayer!!.setOnErrorListener(
                    object : MediaPlayer.OnErrorListener {
                        override fun onError(mp: MediaPlayer?, what: Int, extra: Int): Boolean {
                            synchronized(mLock) {
                                reset()
                                return true
                            }
                        }
                    })
                fis = FileInputStream(audioUri.path)
                mMediaPlayer!!.setDataSource(fis.getFD())
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    val attributes =
                        AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_MEDIA)
                            .build()
                    mMediaPlayer!!.setAudioAttributes(attributes)
                } else {
                    mMediaPlayer!!.setAudioStreamType(AudioManager.STREAM_MUSIC)
                }
                // mMediaPlayer.setAudioStreamType(AudioManager.STREAM_VOICE_CALL)
                mMediaPlayer!!.prepareAsync()
                mMediaPlayer!!.setOnPreparedListener(
                    object : OnPreparedListener {
                        override fun onPrepared(mp: MediaPlayer?) {
                            mMediaPlayer!!.start()
                            _playListener?.onStart(mUriPlaying)

                        }
                    })
            } catch (e: Exception) {
                Log.e(TAG, "startPlay", e)
                _playListener?.onStop(audioUri)
                _playListener = null
                reset()
            } finally {
                if (fis != null) {
                    try {
                        fis.close()
                    } catch (e: IOException) {
                        Log.e(TAG, "startPlay", e)
                    }
                }
            }
        }
    }

    private fun isHeadphonesPlugged(audioManager: AudioManager?): Boolean {
        synchronized(mLock) {
            if (audioManager == null) {
                return false
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val audioDevices =
                    audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS or AudioManager.GET_DEVICES_INPUTS)
                for (deviceInfo in audioDevices) {
                    if (deviceInfo.type == AudioDeviceInfo.TYPE_WIRED_HEADPHONES
                        || deviceInfo.type == AudioDeviceInfo.TYPE_WIRED_HEADSET
                    ) {
                        return true
                    }
                }
                return false
            } else {
                return audioManager.isWiredHeadsetOn()
            }
        }
    }

    fun setPlayListener(listener: IAudioPlayListener?) {
        synchronized(mLock) {
            this._playListener = listener
        }
    }

    fun stopPlay(context: Context?) {
        synchronized(mLock) {
            if (context != null) {
                if (context is Activity) {
                    context.window
                        .clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }
            }
            _playListener?.onStop(mUriPlaying)
            reset()
        }
    }

    private fun reset() {
        resetMediaPlayer()
        resetAudioPlayManager()
    }

    private fun resetAudioPlayManager() {
        if (mAudioManager != null) {
            mAudioManager!!.setMode(AudioManager.MODE_NORMAL)
            muteAudioFocus(mAudioManager, false)
        }
        if (_sensorManager != null) {
            setScreenOn()
            _sensorManager!!.unregisterListener(this)
        }
        _sensorManager = null
        _sensor = null
        _powerManager = null
        mAudioManager = null
        _wakeLock = null
        mUriPlaying = null
        _playListener = null
    }

    private fun resetMediaPlayer() {
        synchronized(mLock) {
            if (mMediaPlayer != null) {
                try {
                    mMediaPlayer!!.stop()
                    mMediaPlayer!!.reset()
                    mMediaPlayer!!.release()
                    mMediaPlayer = null
                } catch (e: IllegalStateException) {
                    Log.e(TAG, "resetMediaPlayer", e)
                }
            }
        }
    }

    fun getPlayingUri(): Uri? {
        synchronized(mLock) {
            return if (mUriPlaying != null) mUriPlaying else Uri.EMPTY
        }
    }

    @TargetApi(Build.VERSION_CODES.FROYO)
    private fun muteAudioFocus(audioManager: AudioManager?, bMute: Boolean) {
        synchronized(mLock) {
            if (audioManager == null) return
            if (bMute) {
                audioManager.requestAudioFocus(
                    afChangeListener,
                    AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
                )
            } else {
                audioManager.abandonAudioFocus(afChangeListener)
                afChangeListener = null
            }
        }
    }

    /**
     * 检查AudioPlayManager是否处于通道正常的状态。
     *
     * @param context 上下文
     * @return 是否处于通道正常的状态
     */
    fun isInNormalMode(context: Context): Boolean {
        synchronized(mLock) {
            if (mAudioManager == null) {
                mAudioManager =
                    context.getApplicationContext()
                        .getSystemService(Context.AUDIO_SERVICE) as AudioManager?
            }
            return mAudioManager != null && mAudioManager!!.getMode() == AudioManager.MODE_NORMAL
        }
    }

    private var isVOIPMode = false

    fun isInVOIPMode(context: Context?): Boolean {
        return isVOIPMode
    }

    fun setInVoipMode(isVOIPMode: Boolean) {
        this.isVOIPMode = isVOIPMode
    }

    fun isPlaying(): Boolean {
        synchronized(mLock) {
            return mMediaPlayer != null && mMediaPlayer!!.isPlaying()
        }
    }

    interface IAudioPlayListener {
        fun onStart(uri: Uri?)

        fun onStop(uri: Uri?)

        fun onComplete(uri: Uri?)
    }

}