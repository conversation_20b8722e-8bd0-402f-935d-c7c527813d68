package com.score.callmetest.manager

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import timber.log.Timber
import java.io.File

/**
 * 铃声管理器
 * 统一管理铃声的播放和停止
 * 使用 MediaPlayer 播放 MP3 文件
 */
object RingtoneManager {
    private const val TAG = "RingtoneManager"
    private var mediaPlayer: MediaPlayer? = null
    private var isPlaying = false

    /**
     * 播放铃声
     * @param context 上下文
     * @param assetFileName assets目录下的音频文件名
     */
    fun playRingtone(context: Context, assetFileName: String = "ring.mp3") {
        Timber.tag(TAG).i("开始播放铃声: $assetFileName")
        // 使用 MediaPlayer 播放铃声
        playWithMediaPlayer(context, assetFileName)
    }

    /**
     * 使用 MediaPlayer 播放铃声
     */
    private fun playWithMediaPlayer(context: Context, assetFileName: String) {
        Timber.tag(TAG).i("开始测试 MediaPlayer 播放 MP3 文件")

        try {
            // 请求音频焦点
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            @Suppress("DEPRECATION")
            val focusResult = audioManager.requestAudioFocus(
                { focusChange -> Timber.tag(TAG).d("MediaPlayer音频焦点变化: $focusChange") },
                AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
            )

            Timber.tag(TAG).d("MediaPlayer音频焦点请求结果: $focusResult")
            Timber.tag(TAG).d("当前音频模式: ${audioManager.mode}")
            Timber.tag(TAG).d("当前音量: ${audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)}")
            Timber.tag(TAG).d("最大音量: ${audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)}")

            stopRingtone()

            // 将 assets 文件复制到 cache 目录
            val cacheFile = copyAssetToCache(context, assetFileName)
            if (cacheFile != null && cacheFile.exists()) {
                mediaPlayer = MediaPlayer()
                // 设置音频流类型
                mediaPlayer?.setAudioStreamType(AudioManager.STREAM_MUSIC)
                // 设置音量
                mediaPlayer?.setVolume(1.0f, 1.0f)
                // 设置监听器
                mediaPlayer?.setOnPreparedListener {
                    Timber.tag(TAG).d("MediaPlayer 准备完成")
                    mediaPlayer?.isLooping = true // 设置循环播放
                    mediaPlayer?.start()
                    isPlaying = true
                }
                mediaPlayer?.setOnErrorListener { mp, what, extra ->
                    Timber.tag(TAG).e("MediaPlayer 错误: what=$what, extra=$extra")
                    false
                }
                mediaPlayer?.setOnCompletionListener {
                    Timber.tag(TAG).d("MediaPlayer 播放完成")
                    isPlaying = false
                }
                // 设置数据源
                mediaPlayer?.setDataSource(cacheFile.absolutePath)
                mediaPlayer?.prepareAsync()
                Timber.tag(TAG).d("MediaPlayer 开始准备: ${cacheFile.absolutePath}")
            } else {
                Timber.tag(TAG).e("无法创建缓存文件")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "测试 MediaPlayer 播放失败")
        }
    }

    /**
     * 将 assets 文件复制到 cache 目录
     */
    private fun copyAssetToCache(context: Context, assetFileName: String): File? {
        return try {
            val inputStream = context.assets.open(assetFileName)
            val cacheFile = File(context.cacheDir, assetFileName)
            
            // 如果文件已存在且大小不为0，直接返回
            if (cacheFile.exists() && cacheFile.length() > 0) {
                Timber.tag(TAG).d("缓存文件已存在: ${cacheFile.absolutePath}, 大小: ${cacheFile.length()} bytes")
                inputStream.close()
                return cacheFile
            }
            
            // 复制文件到 cache 目录
            cacheFile.outputStream().use { outputStream ->
                inputStream.copyTo(outputStream)
            }
            inputStream.close()
            
            Timber.tag(TAG).d("文件复制成功: ${cacheFile.absolutePath}, 大小: ${cacheFile.length()} bytes")
            cacheFile
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "复制 assets 文件到 cache 失败: $assetFileName")
            null
        }
    }

    /**
     * 停止铃声
     */
    fun stopRingtone() {
        try {
            Timber.tag(TAG).d("停止铃声播放")

            if (isPlaying) {
                try {
                    // 停止 MediaPlayer
                    val mp = mediaPlayer
                    if (mp != null) {
                        if (mp.isPlaying) {
                            mp.stop()
                        }
                        mp.release()
                        mediaPlayer = null
                    }
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "停止铃声时发生异常")
                } finally {
                    isPlaying = false
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "停止铃声失败")
            isPlaying = false
        }
    }

    /**
     * 检查是否正在播放
     */
    fun isPlaying(): Boolean {
        return try {
            isPlaying && mediaPlayer != null
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "检查播放状态失败")
            false
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        stopRingtone()
    }
} 