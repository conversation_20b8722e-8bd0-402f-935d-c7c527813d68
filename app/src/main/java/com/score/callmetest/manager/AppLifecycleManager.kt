package com.score.callmetest.manager

import android.app.Activity
import android.app.Application
import android.os.Bundle
import java.lang.ref.WeakReference
import java.util.concurrent.CopyOnWriteArrayList

object AppLifecycleManager {
    private const val TAG = "AppLifecycleManager"
    private var currentActivityRef: WeakReference<Activity>? = null
    private var startedActivityCount = 0
    private var isAppInForeground = false
    private val extraCallbacks = CopyOnWriteArrayList<Application.ActivityLifecycleCallbacks>()

    // 前台切换监听器
    private var foregroundListener: (() -> Unit)? = null

    val activityLifecycleCallbacks = object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            currentActivityRef = WeakReference(activity)
            extraCallbacks.forEach { it.onActivityCreated(activity, savedInstanceState) }
        }
        override fun onActivityStarted(activity: Activity) {
            startedActivityCount++
            val wasInBackground = !isAppInForeground
            isAppInForeground = startedActivityCount > 0
            currentActivityRef = WeakReference(activity)

            // 如果从后台切换到前台，触发监听器
            if (wasInBackground && isAppInForeground) {
                foregroundListener?.invoke()
            }

            extraCallbacks.forEach { it.onActivityStarted(activity) }
        }
        override fun onActivityResumed(activity: Activity) {
            currentActivityRef = WeakReference(activity)
            extraCallbacks.forEach { it.onActivityResumed(activity) }
        }
        override fun onActivityPaused(activity: Activity) {
            extraCallbacks.forEach { it.onActivityPaused(activity) }
        }
        override fun onActivityStopped(activity: Activity) {
            startedActivityCount--
            if (startedActivityCount <= 0) {
                isAppInForeground = false
                // 应用进入后台时清理Activity引用
                currentActivityRef = null
            }
            extraCallbacks.forEach { it.onActivityStopped(activity) }
        }
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            extraCallbacks.forEach { it.onActivitySaveInstanceState(activity, outState) }
        }
        override fun onActivityDestroyed(activity: Activity) {
            // Activity销毁时清理引用
            val currentActivity = currentActivityRef?.get()
            if (currentActivity === activity) {
                currentActivityRef = null
            }
            extraCallbacks.forEach { it.onActivityDestroyed(activity) }
        }
    }

    fun init(application: Application) {
        application.registerActivityLifecycleCallbacks(activityLifecycleCallbacks)
    }

    fun registerExtraLifecycleCallbacks(callbacks: Application.ActivityLifecycleCallbacks) {
        extraCallbacks.add(callbacks)
    }
    fun unregisterExtraLifecycleCallbacks(callbacks: Application.ActivityLifecycleCallbacks) {
        extraCallbacks.remove(callbacks)
    }

    fun isAppInForeground(): Boolean = isAppInForeground
    fun isAppInBackground(): Boolean = !isAppInForeground
    fun getCurrentActivity(): Activity? = currentActivityRef?.get()
    fun getTopActivity(): Activity? = currentActivityRef?.get()

    /**
     * 设置前台切换监听器
     * 当APP从后台切换到前台时会触发
     */
    fun setForegroundListener(listener: (() -> Unit)?) {
        foregroundListener = listener
    }
} 