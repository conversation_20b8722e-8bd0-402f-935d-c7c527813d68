package com.score.callmetest.manager

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.text.SpannableString
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.graphics.toColorInt
import androidx.fragment.app.FragmentActivity
import com.android.billingclient.api.BillingClient.BillingResponseCode
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.Purchase
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.network.CreateRechargeRequest
import com.score.callmetest.network.CreateRechargeResponse
import com.score.callmetest.network.GooglePlayPaymentVerifyRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.PayChannelItem
import com.score.callmetest.network.PurchaseEventType
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.widget.BaseCustomDialog
import com.score.callmetest.ui.widget.PaymentMethodDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber


/**
 * 充值流程管理器
 * 负责处理完整的充值流程，包括订单创建、状态查询、结果处理等
 *
 *
 */
object RechargeManager {

    // 支付渠道缓存
    private var cachedPayChannelList: List<com.score.callmetest.network.PayChannelItem>? = null

    fun cachePayChannelList(channelList: List<com.score.callmetest.network.PayChannelItem>?) {
        cachedPayChannelList = channelList
    }

    fun getCachedPayChannelList(): List<com.score.callmetest.network.PayChannelItem>? {
        return cachedPayChannelList
    }

    /**
     * 开始充值流程
     */
    fun startRecharge(
        activity: Activity,
        goodsCode: String,
        goodsName: String? = null,
        payChannel: String = "GP",
        isSubscription: Boolean = false,
        entry: RechargeSource = RechargeSource.SUBSCRIBE_DETAIL
    ) {
        LoadingUtils.showLoading(activity)
        createRechargeOrder(
            goodsCode = goodsCode,
            payChannel = payChannel,
            entry = entry,
            onSuccess = { data ->
                val orderNo = data.orderNo

                AdjustManager.reportOrderEvent(
                    orderId = orderNo!!,
                    revenue = data.paidAmount!!,
                    currency = data.paidCurrency!!
                )

                // 保存用户选择的支付方式
                PaymentMethodManager.saveLastUsedPaymentMethod(payChannel)

                LogReportManager.reportPurchaseEvent(
                    orderId = orderNo,
                    event = PurchaseEventType.REVIEW_ORDER,
                    code = goodsCode,
                )

                GooglePlayBillingManager.queryProducts(
                    productIds = listOf(goodsCode),
                    isSubscription = isSubscription,
                    onSuccess = { productMap ->
                        if (productMap.isEmpty()) {
                            LogReportManager.reportPurchaseEvent(
                                orderId = orderNo,
                                event = PurchaseEventType.REVIEW_ORDER_RESPONSE,
                                code = goodsCode,
                                result = Constant.FAIL,
                                resultCode = BillingResponseCode.NETWORK_ERROR
                            )

                            LoadingUtils.dismissLoading()
                            return@queryProducts
                        }
                        LogReportManager.reportPurchaseEvent(
                            orderId = orderNo,
                            event = PurchaseEventType.REVIEW_ORDER_RESPONSE,
                            code = goodsCode,
                        )
                        val productDetails = productMap[goodsCode]
                        if (productDetails == null) {
                            Timber.tag("dsc--Billing").e("未找到商品: $goodsCode")
                            LoadingUtils.dismissLoading()
                            return@queryProducts
                        }
                        // 获取 offerToken
                        val offerToken = if (isSubscription) {
                            productDetails.subscriptionOfferDetails?.firstOrNull()?.offerToken
                        } else {
                            productDetails.oneTimePurchaseOfferDetailsList?.firstOrNull()?.offerToken
                        }
                        if (offerToken.isNullOrEmpty()) {
                            Timber.tag("dsc--Billing").e("未获取到有效的 offerToken，无法发起购买")
                            LoadingUtils.dismissLoading()
                            return@queryProducts
                        }
                        val params = BillingFlowParams.newBuilder()
                            .setProductDetailsParamsList(
                                listOf(
                                    BillingFlowParams.ProductDetailsParams.newBuilder()
                                        .setProductDetails(productDetails)
                                        .setOfferToken(offerToken)
                                        .build()
                                )
                            )
                            .setIsOfferPersonalized(false) // 默认无个性化价格，如需支持可动态调整
                            .setObfuscatedAccountId(orderNo)
                            .build()

                        LogReportManager.reportPurchaseEvent(
                            orderId = orderNo,
                            event = PurchaseEventType.LAUNCH_PAY,
                            code = goodsCode,
                        )

                        // 直接购买
                        GooglePlayBillingManager.launchPurchasePage(
                            activity = activity,
                            params = params,
                            onSuccess = { purchase, errCode ->
                                // 拉起支付结束
                                if (purchase == null) {
                                    LogReportManager.reportPurchaseEvent(
                                        orderId = orderNo,
                                        event = PurchaseEventType.LAUNCH_PAY_RESPONSE,
                                        code = goodsCode,
                                        result = Constant.FAIL,
                                        resultCode = errCode
                                    )
                                    return@launchPurchasePage
                                }

                                LogReportManager.reportPurchaseEvent(
                                    orderId = orderNo,
                                    event = PurchaseEventType.LAUNCH_PAY_RESPONSE,
                                    code = goodsCode,
                                )

                                // 如果支付成功，则需要校验
                                LogReportManager.reportPurchaseEvent(
                                    orderId = orderNo,
                                    event = PurchaseEventType.VERIFY_ORDER,
                                    code = goodsCode,
                                )
                                verifyGooglePlayPaymentWithRetry(
                                    orderNo = orderNo,
                                    purchase = purchase,
                                    goodsName = goodsName,
                                    isSubscription = isSubscription,
                                    onSuccess = {
                                        LogReportManager.reportPurchaseEvent(
                                            orderId = orderNo,
                                            event = PurchaseEventType.VERIFY_ORDER_RESPONSE,
                                            code = goodsCode,
                                        )

                                        if (isSubscription) {
                                            LogReportManager.reportPurchaseEvent(
                                                event = PurchaseEventType.ACKNOWLEDGED_ORDER,
                                                orderId = orderNo,
                                                code = goodsCode,
                                            )
                                            GooglePlayBillingManager.acknowledgePurchase(
                                                purchaseToken = purchase.purchaseToken,
                                                onAcknowledged = {
                                                    LogReportManager.reportPurchaseEvent(
                                                        orderId = orderNo,
                                                        event = PurchaseEventType.ACKNOWLEDGED_ORDER_RESPONSE,
                                                        code = goodsCode,
                                                    )
                                                },
                                                onFail = { errCode, msg ->
                                                    LogReportManager.reportPurchaseEvent(
                                                        orderId = orderNo,
                                                        event = PurchaseEventType.ACKNOWLEDGED_ORDER_RESPONSE,
                                                        code = goodsCode,
                                                        result = Constant.FAIL,
                                                        resultCode = errCode
                                                    )
                                                }
                                            )
                                        } else {
                                            LogReportManager.reportPurchaseEvent(
                                                orderId = orderNo,
                                                event = PurchaseEventType.CONSUME_ORDER,
                                                code = goodsCode,
                                            )
                                            GooglePlayBillingManager.consumePurchase(
                                                purchaseToken = purchase.purchaseToken,
                                                onConsumed = {
                                                    LogReportManager.reportPurchaseEvent(
                                                        orderId = orderNo,
                                                        event = PurchaseEventType.CONSUME_ORDER_RESPONSE,
                                                        code = goodsCode,
                                                    )
                                                },
                                                onFail = { errCode, msg ->
                                                    LogReportManager.reportPurchaseEvent(
                                                        orderId = orderNo,
                                                        event = PurchaseEventType.CONSUME_ORDER_RESPONSE,
                                                        code = goodsCode,
                                                        result = Constant.FAIL,
                                                        resultCode = errCode
                                                    )
                                                }
                                            )
                                        }

                                        LoadingUtils.dismissLoading()
                                        handleRechargeSuccess(orderNo = orderNo)
                                        AdjustManager.reportPaySuccessEvent(
                                            orderId = orderNo,
                                            revenue = data.paidAmount,
                                            currency = data.paidCurrency
                                        )
                                    },
                                    onFail = { errCode ->
                                        LogReportManager.reportPurchaseEvent(
                                            orderId = orderNo,
                                            event = PurchaseEventType.VERIFY_ORDER_RESPONSE,
                                            code = goodsCode,
                                            result = Constant.FAIL,
                                            resultCode = errCode
                                        )

                                        // 5分钟检查一次
                                        ThreadUtils.runOnBackgroundDelayed(5 * 60 * 1000L) {
                                            checkPendingOrdersWithGooglePlay()
                                        }

                                        LoadingUtils.dismissLoading()
                                        handleRechargeFailed(orderNo = orderNo, goodsName = goodsName)
//                                        ToastUtils.showToast("Order verification failed, please contact customer service, order number: $orderNo")
                                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.order_verification_failed, orderNo))
                                    }
                                )
                            },
                            onFail = { errCode, msg ->
                                LogReportManager.reportPurchaseEvent(
                                    orderId = orderNo,
                                    event = PurchaseEventType.LAUNCH_PAY_RESPONSE,
                                    code = goodsCode,
                                    result = Constant.SUCCESS,
                                    resultCode = errCode
                                )
                                LoadingUtils.dismissLoading()
//                                ToastUtils.showToast("Purchase fail code: $errCode")
                                ToastUtils.showToast(CallmeApplication.context.getString(R.string.purchase_failed))
                            }
                        )

                        // 跳到购买页时应该取消loading
                        LoadingUtils.dismissLoading()
                    },
                    onFail = { errCode ->
                        LogReportManager.reportPurchaseEvent(
                            orderId = orderNo,
                            event = PurchaseEventType.REVIEW_ORDER_RESPONSE,
                            code = goodsCode,
                            result = Constant.FAIL,
                            resultCode = errCode
                        )

                        LoadingUtils.dismissLoading()
//                        ToastUtils.showToast("queryProducts fail code: $errCode")
                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.sys_error_try_again))
                    }
                )
            },
            onFail = { code, msg ->
                LoadingUtils.dismissLoading()
//                ToastUtils.showToast("Failed to create order: ${msg}")
                ToastUtils.showToast(CallmeApplication.context.getString(R.string.create_order_failed))
            }
        )
    }

    /**
     * 显示支付方式选择弹窗
     */
    private fun showPaymentMethodDialog(
        activity: Context,
        goodsCode: String,
        goodsName: String? = null,
        onSelected: (String) -> Unit = {}
    ) {
        if (activity is FragmentActivity) {
            // 先获取商品信息
            val goodsInfo = findGoodsInfoByCode(goodsCode)
            if (goodsInfo != null) {
                PaymentMethodDialog(goodsInfo) { payChannel ->
                    onSelected.invoke(payChannel)
                }.show(activity.supportFragmentManager, "payment_method_dialog")
            } else {
                // 如果找不到商品信息，使用默认支付渠道
                val defaultChannel = PaymentMethodManager.getDefaultPaymentMethod()
                if (defaultChannel != null) {
                    onSelected.invoke(defaultChannel)
                }
            }
        }
    }

    /**
     * 根据商品编码查找商品信息
     */
    private fun findGoodsInfoByCode(goodsCode: String): com.score.callmetest.network.GoodsInfo? {
        val allGoods = GoodsManager.getCachedAllGoods()
        return allGoods.find { it.code == goodsCode }
    }

    /**
     * 创建充值订单
     */
    private fun createRechargeOrder(
        goodsCode: String,
        payChannel: String,
        entry: RechargeSource,
        onSuccess: ((CreateRechargeResponse) -> Unit)? = null,
        onFail: ((Int, String?) -> Unit)? = null,
    ) {
        LogReportManager.reportPurchaseEvent(
            event = PurchaseEventType.CREATE_ORDER,
            code = goodsCode,
        )
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val request = CreateRechargeRequest(
                    goodsCode = goodsCode,
                    payChannel = payChannel,
                    entry = entry.source
                )

                val response = RetrofitUtils.dataRepository.createRechargeOrder(request)
                withContext(Dispatchers.Main) {
                    if (response is NetworkResult.Success && response.data != null) {
                        LogReportManager.reportPurchaseEvent(
                            event = PurchaseEventType.CREATE_ORDER_RESPONSE,
                            code = goodsCode,
                            orderId = response.data.orderNo
                        )
                        onSuccess?.invoke(response.data)
                    } else {
                        LogReportManager.reportPurchaseEvent(
                            event = PurchaseEventType.CREATE_ORDER_RESPONSE,
                            code = goodsCode,
                            result = Constant.FAIL,
                            resultCode = BillingResponseCode.NETWORK_ERROR
                        )
                        if(response is NetworkResult.Error){
                            onFail?.invoke(response.code?:-1, response.message)
                        }else {
                            onFail?.invoke(-1, "unknown error")
                        }
                    }
                }
            } catch (e: Exception) {
                LogReportManager.reportPurchaseEvent(
                    event = PurchaseEventType.CREATE_ORDER_RESPONSE,
                    code = goodsCode,
                    result = Constant.FAIL,
                    resultCode = BillingResponseCode.NETWORK_ERROR
                )

                withContext(Dispatchers.Main) {
                    onFail?.invoke(BillingResponseCode.NETWORK_ERROR, e.message)
                }
            }
        }
    }

    /**
     * 公共：校验Google Play订单并处理发货、确认/消耗，返回是否成功
     */
    private suspend fun verifyGooglePlayOrder(
        orderNo: String,
        purchase: Purchase,
        isSubscription: Boolean = false
    ): Boolean {
        val verifyReq = GooglePlayPaymentVerifyRequest(
            orderNo = orderNo,
            purchaseData = purchase.originalJson,
            signature = purchase.signature
        )
        val verifyResp = RetrofitUtils.dataRepository.verifyGooglePlayPayment(verifyReq)

        return verifyResp is NetworkResult.Success && verifyResp.data == true
    }

    /**
     * Google Play支付校验，失败最多重试5次，每次间隔1秒
     */
    private fun verifyGooglePlayPaymentWithRetry(
        orderNo: String,
        goodsName: String? = null,
        purchase: Purchase,
        isSubscription: Boolean = false,
        maxRetry: Int = 5,
        delayMillis: Long = 1000L,
        onSuccess: () -> Unit = {},
        onFail: (Int) -> Unit = {},
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            var success = false
            repeat(maxRetry) { attempt ->
                try {
                    success = verifyGooglePlayOrder(orderNo, purchase, isSubscription)
                    if (success) {
                        withContext(Dispatchers.Main) {
                            onSuccess.invoke()
                        }
                        return@launch
                    }
                } catch (e: Exception) {
                    Timber.tag("dsc--Recharge")
                        .e("Google Play 支付校验异常: ${e.message}, attempt=${attempt + 1}")
                }
                Timber.tag("dsc--Recharge")
                    .w("Google Play 校验重试: $orderNo, attempt=${attempt + 1}")
                if (attempt < maxRetry - 1) delay(delayMillis)
            }
            if (!success) {
                withContext(Dispatchers.Main) {
                    onFail.invoke(BillingResponseCode.NETWORK_ERROR)
                }
                Timber.tag("dsc--Recharge")
                    .e("订单校验失败，请联系客服，订单号：$orderNo")
            }
        }
    }

    /**
     * 处理充值成功
     */
    private fun handleRechargeSuccess(orderNo: String) {
        // 刷新用户金币余额
        UserInfoManager.refreshMyUserInfo()

        Timber.tag("dsc--Recharge").i("充值成功，订单号：$orderNo")

        val time = "3 mins"
        val content =
            "We are doing our best to recharge your account. It may take about ${time}. Please be patient."
        val spannable = android.text.SpannableString(content)
        val startIndex = content.indexOf(time.toString())
        val endIndex = startIndex + time.toString().length
        spannable.setSpan(
            android.text.style.ForegroundColorSpan("#FF7F37".toColorInt()),
            startIndex,
            endIndex,
            android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        ActivityUtils.getTopActivity()?.let {
            BaseCustomDialog(
                it,
                emojiSvgaResString = "pay_success.svga",
                title = "Payment Successful!",
                content = spannable,
                agreeText = "Refresh",
                agreeBg = DrawableUtils.createRoundRectDrawable(
                    Color.BLACK,
                    DisplayUtils.dp2pxInternalFloat(24f)
                ),
                agreeTextColor = Color.WHITE,
                cancelText = "OK",
                cancelBg = DrawableUtils.createRoundRectDrawable(
                    "#F3F3F3".toColorInt(),
                    DisplayUtils.dp2pxInternalFloat(24f)
                ),
                cancelTextColor = Color.BLACK,
                bgRes = R.drawable.dialog_bg_green,
                onAgree = {

                },
                onCancel = {

                }
            ).show()
        }
    }

    /**
     * 处理充值失败
     */
    fun handleRechargeFailed(orderNo: String, goodsName: String? = null) {

        Timber.tag("dsc--Recharge").e("充值失败，订单号：$orderNo")

        val title = "Your $goodsName order is processing. We are verifying store payment."
        val content =
            "Contact Customer Service If you've confirmed payment but get no coins in 3 mins."
        ActivityUtils.getTopActivity()?.let {
            BaseCustomDialog(
                it,
                emojiResId = R.drawable.pay_fail,
                title = SpannableString(title).apply {
                    val startIndex = title.indexOf(goodsName.toString())
                    val endIndex = startIndex + goodsName.toString().length
                    setSpan(
                        android.text.style.ForegroundColorSpan("#FF7F37".toColorInt()),
                        startIndex,
                        endIndex,
                        android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                },
                content = content,
                agreeText = "Customer Service",
                agreeBg = DrawableUtils.createRoundRectDrawable(
                    Color.BLACK,
                    DisplayUtils.dp2pxInternalFloat(24f)
                ),
                cancelText = "OK",
                bgRes = R.drawable.dialog_bg_green,
                onAgree = {
                    // todo: jump to service page
                }
            ).apply {
                getAgreeView().apply {
                    setCompoundDrawablesWithIntrinsicBounds(
                        AppCompatResources.getDrawable(
                            it,
                            R.drawable.customer_service_white
                        ), null, null, null
                    )
                    compoundDrawablePadding = DisplayUtils.dp2pxInternal(8f)
                }
                show()
            }
        }
    }

    /**
     * 查询支付渠道列表
     */
    suspend fun fetchPayChannelList(): com.score.callmetest.network.BaseResponse<com.score.callmetest.network.PayChannelResponse>? {
        return try {
            val response = RetrofitUtils.dataRepository.getChannelList()
            Timber.tag("dsc--Recharge").d("查询支付渠道成功: $response")
            if(response is NetworkResult.Success) response.originResp else null
        } catch (e: Exception) {
            Timber.tag("dsc--Recharge").e(e, "查询支付渠道失败")
            null
        }
    }

    /**
     * 确保支付渠道列表已加载，优先返回缓存，无缓存时自动请求并缓存
     */
    suspend fun ensurePayChannelListLoaded(): List<PayChannelItem>? {
        if (cachedPayChannelList != null) return cachedPayChannelList
        val payChannelResponse = fetchPayChannelList()
        val list = payChannelResponse?.data?.channelList
        cachePayChannelList(list)
        return list
    }

    /**
     * 主动预加载支付渠道并缓存，供登录成功后调用，内部处理异常，完成后回调
     */
    fun preloadPayChannelList(onFinish: (() -> Unit)? = null) {
        ThreadUtils.runOnIO {
            try {
                ensurePayChannelListLoaded()
            } catch (e: Exception) {
                Timber.tag("dsc--Recharge").e(e, "拉取支付渠道失败")
            } finally {
                onFinish?.invoke()
            }
        }
    }

    /**
     * 查询Google Play未消耗商品/未确认订阅，与本地订单对比，自动补单
     */
    fun checkPendingOrdersWithGooglePlay() {
        withBillingClientReady {
            GooglePlayBillingManager.queryUnconsumedPurchases { purchase, isSubscription ->
                val orderNo = purchase.accountIdentifiers?.obfuscatedAccountId
                // 没有未完成的就不用再检查补单了
                if (orderNo.isNullOrEmpty()) return@queryUnconsumedPurchases
                verifyGooglePlayPaymentWithRetry(
                    orderNo = orderNo,
                    purchase = purchase,
                    isSubscription = isSubscription,
                    onSuccess = {
                        if (isSubscription) {
                            GooglePlayBillingManager.acknowledgePurchase(purchase.purchaseToken)
                        } else {
                            GooglePlayBillingManager.consumePurchase(purchase.purchaseToken)
                        }
                    },
                    onFail = {
                        // 5分钟检查一次
                        ThreadUtils.runOnBackgroundDelayed(5 * 60 * 1000L) {
                            checkPendingOrdersWithGooglePlay()
                        }
                    }
                )
            }
        }
    }

    // 统一BillingClient初始化入口
    private fun withBillingClientReady(block: () -> Unit) {
        GooglePlayBillingManager.ensureReady {
            block()
        }
    }
} 