package com.score.callmetest.manager

import com.score.callmetest.network.StrategyConfig
import com.score.callmetest.util.SharePreferenceUtil

/**
 * 支付方式管理器
 * 负责管理支付渠道、用户选择、优惠信息等
 */
object PaymentMethodManager {
    
    // 支付渠道常量
    const val PAY_CHANNEL_GP = "GP"           // Google Play
    const val PAY_CHANNEL_PAYPAL = "PAYPAL"   // PayPal
    const val PAY_CHANNEL_STRIPE = "STRIPE"   // Stripe
    
    // SharedPreferences 键
    private const val KEY_LAST_PAYMENT_METHOD = "last_payment_method"
    private const val KEY_IS_FIRST_RECHARGE = "is_first_recharge"
    private const val KEY_PAYMENT_METHOD_RED_DOT = "payment_method_red_dot"
    
    // 当前可用的支付渠道
    private var availablePayChannels: List<String> = emptyList()
    
    // 支付渠道优惠信息
    private val channelDiscounts = mutableMapOf<String, Int>()
    
    /**
     * 初始化支付方式管理器
     */
    fun init(strategyConfig: StrategyConfig?) {
        strategyConfig?.payChannels?.let { channels ->
            availablePayChannels = channels
        }
        
        // 设置本地支付优惠
        strategyConfig?.lpDiscount?.let { discount ->
            channelDiscounts[PAY_CHANNEL_PAYPAL] = discount
            channelDiscounts[PAY_CHANNEL_STRIPE] = discount
        }
        
        // 设置促销商品优惠
        strategyConfig?.lpPromotionDiscount?.let { discount ->
            // 促销商品额外优惠逻辑
        }
    }
    
    /**
     * 获取可用的支付渠道
     */
    fun getAvailablePayChannels(): List<String> = availablePayChannels

    
    /**
     * 获取用户上次使用的支付方式
     */
    fun getLastUsedPaymentMethod(): String? {
        return SharePreferenceUtil.getString(KEY_LAST_PAYMENT_METHOD, PAY_CHANNEL_GP, "payment")
    }
    
    /**
     * 保存用户选择的支付方式
     */
    fun saveLastUsedPaymentMethod(payChannel: String) {
        SharePreferenceUtil.putString(KEY_LAST_PAYMENT_METHOD, payChannel, "payment")
    }
    
    /**
     * 获取默认支付方式
     */
    fun getDefaultPaymentMethod(): String? {
        val lastUsed = getLastUsedPaymentMethod()
        if (lastUsed != null && availablePayChannels.contains(lastUsed)) {
            return lastUsed
        }
        return availablePayChannels.firstOrNull()
    }
    
    /**
     * 检查是否需要显示支付方式选择入口
     * 付费用户且多个支付渠道时显示
     */
    fun shouldShowPaymentMethodSelector(): Boolean {
        return availablePayChannels.size > 1
    }

    /**
     * 获取支付渠道的优惠比例
     */
    fun getChannelDiscount(payChannel: String): Int {
        return channelDiscounts[payChannel] ?: 0
    }
    
    /**
     * 检查支付方式选择入口是否需要显示红点
     */
    fun shouldShowPaymentMethodRedDot(): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_PAYMENT_METHOD_RED_DOT, true, "payment")
    }
    
    /**
     * 隐藏支付方式选择入口红点
     */
    fun hidePaymentMethodRedDot() {
        SharePreferenceUtil.putBoolean(KEY_PAYMENT_METHOD_RED_DOT, false, "payment")
    }

    /**
     * 获取支付渠道显示名称
     */
    fun getPaymentChannelDisplayName(payChannel: String): String {
        return when (payChannel) {
            PAY_CHANNEL_GP -> "Google Play"
            PAY_CHANNEL_PAYPAL -> "PayPal"
            PAY_CHANNEL_STRIPE -> "Credit Card"
            else -> payChannel
        }
    }
    
    /**
     * 获取支付渠道图标资源
     */
    fun getPaymentChannelIcon(payChannel: String): Int {
        return when (payChannel) {
            PAY_CHANNEL_GP -> com.score.callmetest.R.drawable.ic_google
            PAY_CHANNEL_PAYPAL -> com.score.callmetest.R.drawable.coin
            PAY_CHANNEL_STRIPE -> com.score.callmetest.R.drawable.coin
            else -> com.score.callmetest.R.drawable.coin
        }
    }

    /**
     * 清空支付相关本地状态和内存数据
     */
    fun clear() {
        availablePayChannels = emptyList()
        channelDiscounts.clear()
        SharePreferenceUtil.putBoolean(KEY_IS_FIRST_RECHARGE, true, "payment")
        SharePreferenceUtil.putString(KEY_LAST_PAYMENT_METHOD, "", "payment")
        SharePreferenceUtil.putBoolean(KEY_PAYMENT_METHOD_RED_DOT, true, "payment")
    }
} 