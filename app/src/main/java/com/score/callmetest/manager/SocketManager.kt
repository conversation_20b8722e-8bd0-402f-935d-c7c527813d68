package com.score.callmetest.manager

import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.util.Log
import androidx.lifecycle.LifecycleOwner
import com.google.gson.Gson
import com.score.callmetest.Constant
import com.score.callmetest.constants.RongCloudConstants
import com.score.callmetest.constants.SocketCommands
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.HeaderUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import org.json.JSONException
import org.json.JSONObject
import timber.log.Timber
import java.net.URISyntaxException
import java.util.concurrent.ConcurrentHashMap

// Socket消息回调接口
interface LongLiveListener {
    fun onReceive(command: String, commandId: String, data: JSONObject?, extra: Any?)
}

// 请求头拦截器示例，可根据实际需求添加header
class RequestHeaderInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val context = com.score.callmetest.CallmeApplication.context
        val builder = chain.request().newBuilder()
        val headers = HeaderUtils.buildCommonHeaders(context)
        for ((key, value) in headers) {
            builder.addHeader(key, value)
        }
        val request = builder.build()
        return chain.proceed(request)
    }
}


@Serializable
class OnCallMessage(
    val channelName: String?,
    val avatar: String?,
    val avatarThumbUrl: String?,
    val fromUserId: String?,
    val toUserId: String?,
    val videoPlayMode: String?,
    val videoFileUrl: String?,
    val age: Int?,
    val country: String?,
    val isFree: Boolean = false,
    val isFriend: Boolean = false,
    val chooseVideoSdk: Int?,
    val nickname: String?,
    val gender: Int?,
    val unitPrice: Int?,
    val rtcToken: String?,
    val uiTips: String?
)

@Serializable
class OnHangUpMessage(
    val channelName: String?,
    val callCategory: Int?,
    val callType: Int?,
    val isQuickMatch: Boolean?,
    val callMode: String?,
    val clientSessionId: String?,
    val fromUserId: Long?,
    val toUserId: Long?,
    val h5FromUserId: String?,
    val h5ToUserId: String?,
    val reason: Int?,
    val command: String?
)

@Serializable
@Parcelize
class OnPickUpMessage(
    val channelName: String?,
    val callCategory: Int?,
    val clientSessionId: String?,
    val chooseVideoSdk: Int?,
    val rtcToken: String?,
    val fromUserId: Long?,
    val toUserId: Long?,
    val h5FromUserId: String?,
    val h5ToUserId: String?,
    val isFree: Boolean?,
    val broadcasterCallDesc: String?,
    val broadcasterCallDescColourCode: String?,
    val isGreenMode: Boolean?,
    val callFreeSeconds: Int?,
    val levelPowerMessageConfig: LevelPowerMessageConfig?,
    val command: String?
) : Parcelable {
    @Serializable
    @Parcelize
    data class LevelPowerMessageConfig(
        val isOpen: Boolean?
    ) : Parcelable
}

@Serializable
class EstimatedHangUpTimeMessage(
    val channelName: String?,
    val payUserId: String?,
    val estimateTime: Long?
)

@Serializable
class AvailableCoinsMessage(
    val coins: Int?
)

@Serializable
class OnChatMessage(
    val fromUserId: String?,
    val toUserId: String?,
    val content: String?,
    val timestamp: Long?,
    val command: String?
)

// 保留兼容性的别名
typealias MessageEventMessage = OnChatMessage
// endregion

class SocketManager private constructor() {
    private var mSkIo: Socket? = null
    private val conMap = ConcurrentHashMap<String, LongLiveListener>()
    private val mainHandler = Handler(Looper.getMainLooper())
    private var isDes = 0 // 0:连接中, 1:断开

    // 重连相关变量
    private var reconnectAttempts = 0
    private val maxReconnectAttempts = 5
    private val reconnectInterval = 500L // 1秒
    private var isReconnecting = false

    companion object {
        val instance: SocketManager by lazy { SocketManager() }
    }

    private fun connectSocket() {
        val httpClient = OkHttpClient.Builder()
            .addNetworkInterceptor(RequestHeaderInterceptor())
            .build()
        val opts = IO.Options().apply {
            forceNew = true
            reconnection = true // 由我们自己控制重连
            query = "token=${SharePreferenceUtil.getString(Constant.TOKEN_KEY, "")}"
            webSocketFactory = httpClient
            callFactory = httpClient
        }
        try {
            mSkIo = IO.socket("https://" + Constant.getImUrl(), opts)
        } catch (e: URISyntaxException) {
            e.printStackTrace()
        }
        registerEvent()
        mSkIo?.connect()

    }

    fun initAndConnect() {
        reconnectAttempts = 0
        isReconnecting = false
        connectSocket()
        putCommands()
    }

    private fun registerEvent() {
        mSkIo?.on(RongCloudConstants.RESPONSE_EVENT, mEmitterListener)
        mSkIo?.on(RongCloudConstants.MESSAGE_EVENT, mEmitterListener)
        mSkIo?.on(Socket.EVENT_CONNECT_TIMEOUT, mEmitterListener) // todo--dsc 超时好像目前没处理
        mSkIo?.on(Socket.EVENT_DISCONNECT, mDisconnectListener)
        mSkIo?.on(Socket.EVENT_CONNECT_ERROR, mDisconnectListener)
        mSkIo?.on(Socket.EVENT_CONNECT) {
            Log.d("SocketManager", "Socket 已连接成功")
            reconnectAttempts = 0 // 连接成功后重置重连次数
            startHeartbeat() // 连接成功后启动心跳
        }
    }

    private val mEmitterListener = Emitter.Listener { args ->
        mainHandler.post {
            handleCall(args)
        }
    }

    private val mDisconnectListener = Emitter.Listener { args ->
        mainHandler.post {
            handleDisconnect(args)
        }
    }

    private fun handleCall(args: Array<Any>) {
        if (args.isNotEmpty() && args[0] is JSONObject) {
            val obj = args[0] as JSONObject
            Log.d(this.javaClass.name, "handleCall: obj: " + obj)
            if (isDes == 1) return
            try {
                val code = obj.optString("code")
                if (code != "200") return
                val key = obj.optString("command")
                val l = conMap[key] ?: return
                val keyId = obj.optString("commandId")
                val data = obj.optJSONObject("data")
                l.onReceive(key, keyId, data, null)
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
    }

    private fun handleDisconnect(args: Array<Any>) {
        Log.d(this.javaClass.name, "handleDisconnect: " + args.toString())
        if (isDes == 1) return // 主动断开不重连
        if (!isReconnecting && reconnectAttempts < maxReconnectAttempts) {
            isReconnecting = true
            reconnectAttempts++
            Log.d("SocketManager", "Socket断开，准备第${reconnectAttempts}次重连...")
            mainHandler.postDelayed({
                reconnect()
            }, reconnectInterval)
        } else if (reconnectAttempts >= maxReconnectAttempts) {
            Log.d("SocketManager", "Socket重连已达最大次数，停止重连")
        }
    }

    private fun reconnect() {
        if (isDes == 1) return // 主动断开不重连
        mSkIo?.off()
        connectSocket()
        isReconnecting = false
    }

    fun putCommands() {
        val EVT_LIST = SocketCommands.getAllCommands()
        for (evt in EVT_LIST) {
            conMap[evt] = object : LongLiveListener {
                override fun onReceive(
                    command: String,
                    commandId: String,
                    data: JSONObject?,
                    extra: Any?
                ) {
                    Log.d("SocketManager", "收到事件: ${SocketCommands.getCommandDisplayName(command)}, id: $commandId, data: $data")
                    if (data == null) return
                    try {
                        val gson = Gson()
                        when (command) {
                            SocketCommands.Call.ON_CALL -> {
                                val msg = gson.fromJson(data.toString(), OnCallMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onCall", msg.channelName, msg.fromUserId, msg.toUserId)
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Call.ON_HANG_UP -> {
                                val msg = gson.fromJson(data.toString(), OnHangUpMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onHangUp", msg.channelName, msg.fromUserId?.toString(), msg.toUserId?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Call.ON_PICK_UP -> {
                                // note.这里的接听，是对方接听。经检测，对方打过来，这里点击接听，是不触发这个的。
                                val msg = gson.fromJson(data.toString(), OnPickUpMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onPickUp", msg.channelName, msg.fromUserId?.toString(), msg.toUserId?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Call.ESTIMATED_HANG_UP_TIME -> {
                                val msg = gson.fromJson(data.toString(), EstimatedHangUpTimeMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("estimatedHangUpTime", msg.channelName, msg.payUserId, null)
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Coins.AVAILABLE_COINS -> {
                                val msg = gson.fromJson(data.toString(), AvailableCoinsMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("availableCoins", null, null, null, msg.coins?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Message.ON_CHAT,
                            SocketCommands.Message.MESSAGE_EVENT -> {
                                val msg = gson.fromJson(data.toString(), OnChatMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onChat", null, msg.fromUserId, msg.toUserId, msg.timestamp?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Order.RECHARGE_ORDER_STATUS -> {
                                // 充值订单状态处理
                            }
                            Socket.EVENT_CONNECT_TIMEOUT -> {
                                // 超时
                            }
                            else -> {
                                Log.w("SocketManager", "未处理的事件类型: $command")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("SocketManager", "处理事件失败: ${e.message}", e)
                    }
                }
            }
        }
    }

    fun sendMessage(event: String, data: JSONObject) {
        mSkIo?.emit(event, data)
    }

    // 心跳上报
    private var heartbeatHandler: Handler? = null
    private var heartbeatRunnable: Runnable? = null
    fun startHeartbeat() {
        if (heartbeatHandler == null) heartbeatHandler = Handler(Looper.getMainLooper())
        if (heartbeatRunnable == null) {
            heartbeatRunnable = object : Runnable {
                override fun run() {
                    reportHeartbeat()
                    heartbeatHandler?.postDelayed(this, 60_000)
                }
            }
        }
        heartbeatHandler?.post(heartbeatRunnable!!)
    }

    fun stopHeartbeat() {
        heartbeatHandler?.removeCallbacks(heartbeatRunnable!!)
    }

    private fun reportHeartbeat() {
        ThreadUtils.runOnIO {
            // 调用接口 /user/activeing, 无参
            try {
                RetrofitUtils.dataRepository.reportUserActive()
                Timber.tag("SocketManager").d("上报心跳")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun isConnected(): Boolean {
        return mSkIo?.connected() as Boolean
    }

    fun disconnect() {
        isDes = 1
        mSkIo?.disconnect()
        mSkIo?.off()
        stopHeartbeat()
        reconnectAttempts = 0
        isReconnecting = false
    }

    @Deprecated("使用 DualChannelEventManager.observeOnCall 替代", ReplaceWith("DualChannelEventManager.observeOnCall(owner, onEvent)"))
    fun observeOnCall(owner: LifecycleOwner, onEvent: (OnCallMessage) -> Unit) {
        EventBus.observe(owner, OnCallMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeOnHangUp 替代", ReplaceWith("DualChannelEventManager.observeOnHangUp(owner, onEvent)"))
    fun observeOnHangUp(owner: LifecycleOwner, onEvent: (OnHangUpMessage) -> Unit) {
        EventBus.observe(owner, OnHangUpMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeOnPickUp 替代", ReplaceWith("DualChannelEventManager.observeOnPickUp(owner, onEvent)"))
    fun observeOnPickUp(owner: LifecycleOwner, onEvent: (OnPickUpMessage) -> Unit) {
        EventBus.observe(owner, OnPickUpMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeEstimatedHangUpTime 替代", ReplaceWith("DualChannelEventManager.observeEstimatedHangUpTime(owner, onEvent)"))
    fun observeEstimatedHangUpTime(owner: LifecycleOwner, onEvent: (EstimatedHangUpTimeMessage) -> Unit) {
        EventBus.observe(owner, EstimatedHangUpTimeMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeAvailableCoins 替代", ReplaceWith("DualChannelEventManager.observeAvailableCoins(owner, onEvent)"))
    fun observeAvailableCoins(owner: LifecycleOwner, onEvent: (AvailableCoinsMessage) -> Unit) {
        EventBus.observe(owner, AvailableCoinsMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeOnChat 替代", ReplaceWith("DualChannelEventManager.observeOnChat(owner, onEvent)"))
    fun observeOnChat(owner: LifecycleOwner, onEvent: (OnChatMessage) -> Unit) {
        EventBus.observe(owner, OnChatMessage::class.java, onEvent = onEvent)
    }

    // 保留兼容性
    @Deprecated("使用 observeOnChat 替代", ReplaceWith("observeOnChat(owner, onEvent)"))
    fun observeMessageEvent(owner: LifecycleOwner, onEvent: (MessageEventMessage) -> Unit) {
        observeOnChat(owner, onEvent)
    }
}