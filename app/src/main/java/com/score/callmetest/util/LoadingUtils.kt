package com.score.callmetest.util

import android.app.Activity
import android.app.Dialog
import android.view.ViewGroup
import android.view.Window
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser

object LoadingUtils {
    private var dialog: Dialog? = null
    fun showLoading(activity: Activity, cancelable: Boolean = false) {
        if (dialog?.isShowing == true) return
        dialog = Dialog(activity)
        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val svgaView = SVGAImageView(activity)
        svgaView.setBackgroundColor(android.graphics.Color.TRANSPARENT)
        dialog?.setContentView(
            svgaView,
            ViewGroup.LayoutParams(
                DisplayUtils.dp2px(70f),
                DisplayUtils.dp2px(70f)
            )
        )
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(cancelable)
        dialog?.show()
        // 播放loading.svga
        val parser = SVGAParser(activity)
        parser.decodeFromAssets("loading.svga", object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                svgaView.setImageDrawable(SVGADrawable(videoItem))
                svgaView.loops = 0 // 无限循环
                svgaView.startAnimation()
            }

            override fun onError() {
                // 可选：处理加载失败
            }
        })
    }

    fun dismissLoading() {
        dialog?.dismiss()
        dialog = null
    }
} 