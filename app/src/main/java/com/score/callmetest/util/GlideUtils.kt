package com.score.callmetest.util

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.BitmapImageViewTarget

object GlideUtils {
    /**
     * 加载图片（支持url、资源id、文件），可选圆角、圆形、占位图、错误图
     * 新增：自动补足图片为目标ImageView的宽高比，避免内容被裁剪
     */
    @SuppressLint("CheckResult")
    fun load(
        context: Context,
        url: Any?,
        imageView: ImageView,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false,
        vararg extraTransform: Transformation<Bitmap>
    ) {
        val request = Glide.with(context).asBitmap().load(url)
        placeholder?.let { request.placeholder(it) }
        error?.let { request.error(it) }
        request.diskCacheStrategy(DiskCacheStrategy.ALL)
        val options = when {
            isCircle -> RequestOptions().transform(CircleCrop(),*extraTransform)
            radius > 0 -> RequestOptions().transform(RoundedCorners(radius),*extraTransform)
            else -> RequestOptions().transform(*extraTransform) // 不做任何裁剪或缩放变换
        }
        request.apply(options).into(object : BitmapImageViewTarget(imageView) {
            override fun setResource(resource: Bitmap?) {
                if (resource == null) {
                    imageView.setImageBitmap(null)
                    return
                }
                // 只加载原图，不做任何补白或变换
                imageView.setImageBitmap(resource)
            }
        })
    }

    fun load(
        activity: Activity,
        url: Any?,
        imageView: ImageView,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false
    ) = load(activity as Context, url, imageView, placeholder, error, radius, isCircle)

    fun load(
        fragment: Fragment,
        url: Any?,
        imageView: ImageView,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false
    ) = load(fragment.requireContext(), url, imageView, placeholder, error, radius, isCircle)

    fun load(
        view: ImageView,
        url: Any?,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false
    ) = load(view.context, url, view, placeholder, error, radius, isCircle)

    /**
     * 清除图片缓存
     */
    fun clear(context: Context, imageView: ImageView) {
        Glide.with(context).clear(imageView)
    }
} 