package com.score.callmetest.util

import android.content.Context
import android.content.res.Configuration
import android.icu.text.Transliterator
import android.os.Build
import java.util.Locale
import androidx.core.content.edit

object LanguageUtils {
    private const val PREF_KEY_LANGUAGE = "app_language"

    fun setAppLanguage(context: Context, language: String) {
        val sp = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        sp.edit { putString(PREF_KEY_LANGUAGE, language) }
        updateAppLanguage(context, language)
    }

    fun getAppLanguage(context: Context): String {
        val sp = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        return sp.getString(PREF_KEY_LANGUAGE, getSystemLanguage()) ?: getSystemLanguage()
    }

    fun updateAppLanguage(context: Context, language: String) {
        val locale = Locale(language)
        Locale.setDefault(locale)
        val config = Configuration(context.resources.configuration)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            config.setLocale(locale)
            context.createConfigurationContext(config)
        } else {
            config.locale = locale
            context.resources.updateConfiguration(config, context.resources.displayMetrics)
        }
    }

    fun getSystemLanguage(): String {
        return Locale.getDefault().language
    }


//    /**
//     * 汉语拼音转换
//     * @param [chinese] 中文
//     * @param [separator] 分隔符
//     * @return [String]
//     */
//    fun chineseToPinyin(chinese: String,separator: String = ""): String {
//        return Pinyin.toPinyin(chinese, separator).lowercase()
//    }
} 