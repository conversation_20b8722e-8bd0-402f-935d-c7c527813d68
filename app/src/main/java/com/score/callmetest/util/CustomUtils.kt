package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.db.AppDatabase
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.login.LoginActivity
import com.score.callmetest.ui.mine.SettingsActivity
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 当前项目抽离出公用的一些功能
 */
object CustomUtils {

    /**
     * 格式化时间戳
     *
     * <1天（显示hh:mm）
     * 大于1天（显示MM-dd hh:mm）
     * 非今年消息（yyyy-MM-DD hh:mm）
     */
    fun formatTimestampForChatList(timestamp: Long): String {
        val calendar = Calendar.getInstance()
        val currentYear = calendar.get(Calendar.YEAR)
        val currentDay = calendar.get(Calendar.DAY_OF_YEAR)

        val date = Date(timestamp)
        val messageCalendar = Calendar.getInstance().apply {
            time = date
        }
        val messageYear = messageCalendar.get(Calendar.YEAR)
        val messageDay = messageCalendar.get(Calendar.DAY_OF_YEAR)

        return when {
            // 今天内的消息，显示时分
            currentYear == messageYear && currentDay == messageDay -> {
                SimpleDateFormat("HH:mm", Locale.getDefault()).format(date)
            }
            // 非今年的消息，显示年月日时分
            currentYear != messageYear -> {
                SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(date)
            }
            // 今年内但不是今天的消息，显示月日时分
            else -> {
                SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(date)
            }
        }
    }

    /**
     * 获取格式化的时间
     * @return 格式化的时间字符串
     */
    fun getFormattedTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val date = Date(timestamp)
        val sdf = when {
            isToday(timestamp, now) -> SimpleDateFormat("HH:mm", Locale.getDefault())
            isYesterday(timestamp, now) -> return "昨天"
            isThisYear(timestamp, now) -> SimpleDateFormat("MM-dd", Locale.getDefault())
            else -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        }
        return sdf.format(date)
    }

    /**
     * 判断是否是今天
     */
    private fun isToday(timestamp: Long, now: Long): Boolean {
        val sdf = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        return sdf.format(timestamp) == sdf.format(now)
    }

    /**
     * 判断是否是昨天
     */
    private fun isYesterday(timestamp: Long, now: Long): Boolean {
        val sdf = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        val yesterdayDate = Date(now - 24 * 60 * 60 * 1000)
        return sdf.format(timestamp) == sdf.format(yesterdayDate)
    }

    /**
     * 判断是否是今年
     */
    private fun isThisYear(timestamp: Long, now: Long): Boolean {
        val sdf = SimpleDateFormat("yyyy", Locale.getDefault())
        return sdf.format(timestamp) == sdf.format(now)
    }


    /**
     *  播放一次svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     */
    fun playSvgaOnce(svgaView: SVGAImageView, assetName: String?) {
        playSvga(svgaView,assetName,1)
    }

    /**
     *  播放svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     * @param [loops] 播放次数--默认无限
     */
    fun playSvga(svgaView: SVGAImageView, assetName: String?,loops: Int = 0) {
        if (assetName == null) return
        SVGAParser.shareParser().apply {
            init(CallmeApplication.context)
            decodeFromAssets(assetName,object : SVGAParser.ParseCompletion {
                override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                    svgaView.setImageDrawable(SVGADrawable(videoItem))
                    svgaView.loops = loops
                    svgaView.startAnimation()
                }

                override fun onError() {}
            })
        }
    }

    /**
     * 退出登录时清理所有本地数据、SDK资源，并跳转到登录页
     * @param [type] 类型  1-清理
     *                     0-正常退出
     */
    fun logoutAndClearData(type: Int = 0,context: Context) {
        GlobalManager.onLogout() // 重置全局状态 声网SDK登出


        // 目前表没有用当前登录userId区分，所以退出登录就全删
        // 用协程在IO线程清空数据库，清理完成后再跳转
        /*   lifecycleScope.launch {
               try {
                   withContext(Dispatchers.IO) {
                       AppDatabase.getInstance(applicationContext).clearAllTablesData()
                   }
               } catch (e: Exception) {
                   e.printStackTrace()
               }
               gotoLogin()
               ToastUtils.showToast(getString(R.string.logout))
           }*/
        if (type == 1) {
            // 用协程在IO线程清空数据库，清理完成后再跳转
            ThreadUtils.runOnIO {
                try {
                    AppDatabase.getInstance(context).clearAllTablesData()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                gotoLogin(context)
                ToastUtils.showToast(context.getString(R.string.delete_account))
            }
        } else {
            gotoLogin(context)
            if(ActivityUtils.getTopActivity() is SettingsActivity) {
                ToastUtils.showToast(context.getString(R.string.logout))
            }
        }
    }

    /**
     * 转到登录
     * @param [context] 上下文
     */
    fun gotoLogin(context: Context) {
        if(ActivityUtils.getTopActivity() is LoginActivity) return
        val intent = Intent(context, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        context.startActivity(intent)
        if(context is Activity) {
            context.finish()
        }
    }

    /**
     * 根据礼物名称获取对应的资源ID
     *
     * @param giftCode 礼物code
     * @return 资源ID
     */
    fun getGiftResIdById(giftCode: String?): Int {
        // 这里实现根据礼物名称查找资源ID的逻辑
        return when (giftCode) {
            "1" -> R.drawable.kiss  // 文档香蕉、kiss、青瓜都有。。
            "2" -> R.drawable.meigui // 一支玫瑰、巧克力
            "3" -> R.drawable.bear // 熊
            "4" -> R.drawable.lipstick // 唇膏
            "5" -> R.drawable.xiangbin // 香槟、香水
            "6" -> R.drawable.rose // 一束玫瑰
            "7" -> R.drawable.shouzhuo  // 表耳环手镯--我选手镯
            "8" -> R.drawable.crystal_shoes // 水晶鞋
            "9" -> R.drawable.yongyi // 泳衣
            "10" -> R.drawable.hunsha // 婚纱
            "11" -> R.drawable.aimashibao // 爱马仕包
            "12" -> R.drawable.ring // 戒指
            "13" -> R.drawable.chuang // 床
            "14" -> R.drawable.xianglian // 砖石项链
            "15" -> R.drawable.huangguan // 皇冠
            "16" -> R.drawable.paoche // 跑车
            "17" -> R.drawable.bieshu // 别墅
            "18" -> R.drawable.feiji // 飞机
            "19" -> R.drawable.youlun // 油轮
            "20" -> R.drawable.chengbao // 城堡
            else -> R.drawable.gift_placehold
        }
    }
}