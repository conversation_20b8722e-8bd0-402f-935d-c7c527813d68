package com.score.callmetest.util

import com.score.callmetest.R

/**
 * 国家管理工具类，包含常见国家的中英文名、区号、ISO代码、图标等信息
 */
object CountryUtils {
    data class Country(
        val zhName: String,
        val enName: String,
        val code: String, // 区号，如 +86
        val iso: String,  // ISO 2位代码，如 CN
        val iconRes: Int? = null // 国家图标资源id，可为null
    )

    // 只列出HomeFragment中用到的国家，iconRes需在R.mipmap下定义
    private val countryList = listOf(
        Country("哥伦比亚", "Colombia", "+57", "CO", com.score.callmetest.R.drawable.co),
        Country("摩洛哥", "Morocco", "+212", "MA", com.score.callmetest.R.drawable.ma),
        Country("巴西", "Brazil", "+55", "BR", com.score.callmetest.R.drawable.br),
        Country("委内瑞拉", "Venezuela", "+58", "VE", com.score.callmetest.R.drawable.ve),
        Country("越南", "Vietnam", "+84", "VN", com.score.callmetest.R.drawable.vn),
        Country("乌克兰", "Ukraine", "+380", "UA", com.score.callmetest.R.drawable.ua),
        Country("菲律宾", "Philippines", "+63", "PH", com.score.callmetest.R.drawable.ph),
        Country("印度", "India", "+91", "IN", com.score.callmetest.R.drawable.`in`),
        Country("泰国", "Thailand", "+66", "TH", com.score.callmetest.R.drawable.th),
        Country("秘鲁", "Peru", "+51", "PE", com.score.callmetest.R.drawable.pe),
        Country("厄瓜多尔", "Ecuador", "+593", "EC", com.score.callmetest.R.drawable.ec),
        Country("土耳其", "Turkey", "+90", "TR", com.score.callmetest.R.drawable.tr),
        Country("阿根廷", "Argentina", "+54", "AR", com.score.callmetest.R.drawable.ar),
        Country("阿塞拜疆", "Azerbaijan", "+994", "AZ", com.score.callmetest.R.drawable.az),
        Country("美国", "United States", "+1", "US", com.score.callmetest.R.drawable.us),
        Country("俄罗斯", "Russia", "+7", "RU", com.score.callmetest.R.drawable.ru),
        Country("印尼", "Indonesia", "+62", "ID", com.score.callmetest.R.drawable.id),
        // 其它常见国家（无icon）
       // Country("中国", "China", "+86", "CN", null),
        Country("日本", "Japan", "+81", "JP", null),
        Country("韩国", "South Korea", "+82", "KR", null),
        Country("英国", "United Kingdom", "+44", "GB", null),
        Country("法国", "France", "+33", "FR", null),
        Country("德国", "Germany", "+49", "DE", null),
        Country("加拿大", "Canada", "+1", "CA", null),
        Country("澳大利亚", "Australia", "+61", "AU", null),
        Country("新加坡", "Singapore", "+65", "SG", null),
        Country("马来西亚", "Malaysia", "+60", "MY", null),
        Country("墨西哥", "Mexico", "+52", "MX", null),
        Country("意大利", "Italy", "+39", "IT", null)
    )

    /** 获取全部国家列表 */
    fun getAllCountries(): List<Country> = countryList

    /** 获取所有有icon的国家列表 */
    fun getAllCountriesWithIcon(): List<Country> = countryList.filter { it.iconRes != null }

    /** 通过ISO代码查找有icon国家 */
    fun getCountryIconResByIso(iso: String?): Int {
        return getCountryByIso(iso ?: "")?.iconRes ?: com.score.callmetest.R.drawable.map_language
    }

    /** 通过区号查找国家 */
    fun getCountryByCode(code: String): Country? = countryList.find { it.code == code }

    /** 通过ISO代码查找国家 */
    fun getCountryByIso(iso: String): Country? = countryList.find { it.iso.equals(iso, true) }
    /** 通过中文名查找国家 */
    fun getCountryByZhName(zhName: String): Country? = countryList.find { it.zhName == zhName }

    /** 通过英文名查找国家 */
    fun getCountryByEnName(enName: String): Country? = countryList.find { it.enName.equals(enName, true) }

    /** 获取当前国家（根据系统Locale） */
    fun getCurrentCountry(): Country? {
        val locale = java.util.Locale.getDefault()
        // 优先用ISO代码匹配
        getCountryByIso(locale.country)?.let { return it }
        // 其次用英文名匹配
        getCountryByEnName(locale.displayCountry)?.let { return it }
        return null
    }

    /** 通过英文名查找图标 */
    fun getIconByEnName(enName: String?): Int {
        return getCountryByEnName(enName ?: "")?.iconRes ?: R.drawable.map_language
    }

}