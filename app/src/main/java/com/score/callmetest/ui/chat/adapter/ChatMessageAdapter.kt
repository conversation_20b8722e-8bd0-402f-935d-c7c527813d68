package com.score.callmetest.ui.chat.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.databinding.ItemChatMessageGiftBinding
import com.score.callmetest.databinding.ItemChatMessageLeftBinding
import com.score.callmetest.databinding.ItemChatMessageRightBinding
import com.score.callmetest.databinding.ItemChatMessageVoiceLeftBinding
import com.score.callmetest.databinding.ItemChatMessageVoiceRightBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.entity.MessageType

/**
 * 聊天消息适配器
 * 支持多种消息类型和左右布局
 */
class ChatMessageAdapter : ListAdapter<ChatMessageEntity, RecyclerView.ViewHolder>(MessageDiffCallback()) {

    /**
     *  消息涉及到的所有listener
     */
    private val mChatMessageListeners = ChatAdapterListeners()

    companion object {
        private const val VIEW_TYPE_SENT_TEXT = 1     // 发送的文本消息
        private const val VIEW_TYPE_RECEIVED_TEXT = 2 // 接收的文本消息
        private const val VIEW_TYPE_SENT_IMAGE = 3    // 发送的图片消息
        private const val VIEW_TYPE_RECEIVED_IMAGE = 4 // 接收的图片消息
        private const val VIEW_TYPE_SENT_VOICE = 5    // 发送的语音消息
        private const val VIEW_TYPE_RECEIVED_VOICE = 6 // 接收的语音消息
        private const val VIEW_TYPE_GIFT = 7          // 礼物消息

        private const val PAY_LOAD_STATUS = 0x101          // 发送状态更新
        private const val PAY_LOAD_AUDIO_PLAY = 0x102          // 语音播放动画更新
    }

    // <editor-folder desc="对外事件">

    /**
     * 设置消息点击监听(text、voice)
     */
    fun setOnMessageClickListener(listener: (ChatMessageEntity, View) -> Unit) {
        mChatMessageListeners.mOnMessageClickListener= listener
    }

    /**
     * 设置消息长按监听(text、voice)
     */
    fun setOnMessageLongClickListener(listener: (ChatMessageEntity, View) -> Boolean) {
        mChatMessageListeners.mOnMessageLongClickListener = listener
    }

    /**
     * 设置重发按钮点击监听
     */
    fun setOnResendClickListener(listener: (ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnResendClickListener = listener
    }

    /**
     * 设置图片点击监听
     */
    fun setOnImageClickListener(listener: (ChatMessageEntity, View) -> Unit) {
        mChatMessageListeners.mOnImageClickListener = listener
    }

    /**
     * 设置图长按片点击监听
     */
    fun setOnImageLongClickListener(listener: (ChatMessageEntity, View) -> Boolean) {
        mChatMessageListeners.mOnImageLongClickListener = listener
    }

    /**
     * 设置头像点击监听
     */
    fun setOnAvatarClickListener(listener: (ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnAvatarClickListener = listener
    }

    /**
     * 设置翻译点击监听
     */
    fun setOnTranslateClickListener(listener: (ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnTranslateClickListener = listener
    }

    /**
     * 设置FAQ问题点击监听器
     * @param listener FAQ问题点击监听器，参数为(faqCode: Int, message: ChatMessageEntity) -> Unit
     */
    fun setOnFaqQuestionClickListener(listener: (Int, ChatMessageEntity) -> Unit) {
        mChatMessageListeners.mOnFaqQuestionClickListener = listener
    }

    // </editor-folder>

    override fun getItemViewType(position: Int): Int {
        val message = getItem(position)
        return when (message.messageType) {
            MessageType.TEXT -> {
                if (message.isCurrentUser) VIEW_TYPE_SENT_TEXT else VIEW_TYPE_RECEIVED_TEXT
            }
            MessageType.IMAGE -> {
                if (message.isCurrentUser) VIEW_TYPE_SENT_IMAGE else VIEW_TYPE_RECEIVED_IMAGE
            }
            MessageType.VOICE -> {
                if (message.isCurrentUser) VIEW_TYPE_SENT_VOICE else VIEW_TYPE_RECEIVED_VOICE
            }
            MessageType.GIFT -> {
                VIEW_TYPE_GIFT
            }
            MessageType.LINK -> {
                // 这个都是主播发送给用户的--算是文本
                VIEW_TYPE_RECEIVED_TEXT
            }
            MessageType.SYSTEM -> {
                // FAQ消息都是客服发送给用户的
                VIEW_TYPE_RECEIVED_TEXT
            }
            else -> {
                if (message.isCurrentUser) VIEW_TYPE_SENT_TEXT else VIEW_TYPE_RECEIVED_TEXT
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_SENT_TEXT, VIEW_TYPE_SENT_IMAGE -> {
                // 文本、图片发送
                val binding = ItemChatMessageRightBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SentMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_RECEIVED_TEXT, VIEW_TYPE_RECEIVED_IMAGE -> {
                // 文本、图片接收
                val binding = ItemChatMessageLeftBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ReceivedMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_SENT_VOICE -> {
                // 语音发送
                val binding = ItemChatMessageVoiceRightBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SentVoiceMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_RECEIVED_VOICE -> {
                // 语音接收
                val binding = ItemChatMessageVoiceLeftBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ReceivedVoiceMessageViewHolder(binding, mChatMessageListeners)
            }
            VIEW_TYPE_GIFT -> {
                // 礼物发送
                val binding = ItemChatMessageGiftBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                GiftMessageViewHolder(binding, mChatMessageListeners)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: List<Any?>) {
        if(payloads.isEmpty()){
            super.onBindViewHolder(holder, position, payloads)
            return
        }
        // 判断局部修改位置
        val payLoad = payloads[0]
        if(payLoad == PAY_LOAD_STATUS){
            (holder as MessageHolder).updateStatus(getItem(position).status)
            return
        }

        when (holder) {
            is SentMessageViewHolder -> {
            }
            is ReceivedMessageViewHolder -> {
            }
            is SentVoiceMessageViewHolder, is ReceivedVoiceMessageViewHolder -> {
                // 语音消息
                val msg = getItem(position).apply {
                    if(isPlaying) {
                        holder.startPlayAnimation()
                    }else {
                        holder.stopPlayAnimation()
                    }
                    isChange = false
                }
                holder.updateEntity(msg)
            }
            is GiftMessageViewHolder -> {

            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val message = getItem(position)
        when (holder) {
            is SentMessageViewHolder -> holder.bind(message)
            is ReceivedMessageViewHolder -> holder.bind(message)
            is SentVoiceMessageViewHolder,is ReceivedVoiceMessageViewHolder -> {
                // 语音消息
                holder.bind(message)

                // 如果这个消息正在播放，显示播放动画
                if (getItem(position).isPlaying) {
                    holder.startPlayAnimation()
                } else {
                    holder.stopPlayAnimation()
                }
            }
            is GiftMessageViewHolder -> holder.bind(message)
        }
    }

    /**
     * 消息差异比较回调
     */
    private class MessageDiffCallback : DiffUtil.ItemCallback<ChatMessageEntity>() {
        override fun areItemsTheSame(oldItem: ChatMessageEntity, newItem: ChatMessageEntity): Boolean {
            return oldItem.messageId == newItem.messageId
        }

        override fun areContentsTheSame(oldItem: ChatMessageEntity, newItem: ChatMessageEntity): Boolean {
            val same = oldItem == newItem
            return same
//            return (oldItem == newItem) && // 内存--是否同一个对象
//                    !newItem.isChange && // isChange = true则需要部分更改--目前只有语音动画
//                    (oldItem.status == newItem.status) // 发送状态更改
        }

        override fun getChangePayload(oldItem: ChatMessageEntity, newItem: ChatMessageEntity): Any? {
            if(oldItem.status != newItem.status){
                // 更新状态
                return PAY_LOAD_STATUS
            }
            when (newItem.messageType) {
                MessageType.VOICE -> {
                    // 语音 -- 播放是否
                    return if(newItem.isChange) {
                        PAY_LOAD_AUDIO_PLAY
                    }else {
                        // 发送状态或其他更改
                        null
                    }
                }
                MessageType.IMAGE -> {
                    // 图片
                }
                else -> return null
            }
            return null
        }
    }
} 