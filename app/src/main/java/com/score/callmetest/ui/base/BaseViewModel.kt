package com.score.callmetest.ui.base

import android.content.Intent
import android.os.Build
import android.provider.Settings
import android.util.Base64
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.LoginData
import com.score.callmetest.network.LoginRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.RiskInfo
import com.score.callmetest.network.StrategyConfig
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.util.AESUtils
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.GPSUtils
import com.score.callmetest.util.InputMethodUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.LocaleUtils
import com.score.callmetest.util.NetworkUtils
import com.score.callmetest.util.ProxyUtils
import com.score.callmetest.util.SIMUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeZoneUtils

import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive
import retrofit2.HttpException
import java.io.IOException

open class BaseViewModel : ViewModel() {
    // BaseViewModel现在只作为基础类，具体业务逻辑移到对应的ViewModel中


    fun doLogin(
        oauthType: Int,
        token: String,
        onSuccess: (LoginData) -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                Log.d("LoginViewModel", "before execute login request")
                val relogin = SharePreferenceUtil.getInt(Constant.RELOGIN, 0)
                val kFactor = AppConfigManager.getRiskControlConfig()?.k_factor ?: ""
                val systemLanguage = LocaleUtils.getSystemLanguageCountry()
                val riskInfo = RiskInfo(
                    platform = Constant.HEADER_PLATFORM_VALUE,
                    pkg = CallmeApplication.context.packageName,
                    ver = BuildConfig.VERSION_NAME,
                    platform_ver = Build.VERSION.SDK_INT.toString(),
                    model = Build.MODEL,
//                    user_id = userId,
                    device_id = DeviceUtils.getAndroidId(),
                    is_enable_vpn = if (NetworkUtils.isVPNEnabled()) "1" else "0",
                    is_enable_proxy = if (ProxyUtils.isProxyEnabled()) "1" else "0",
                    system_language = systemLanguage,
                    sim_country = SIMUtils.getSIMCountry(),
                    time_zone = TimeZoneUtils.getCurrentTimeZoneId() ?: "",
                    is_auto_time_zone = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
                        Settings.Global.getInt(
                            CallmeApplication.context.contentResolver,
                            Settings.Global.AUTO_TIME_ZONE, 0
                        ).toString()
                    } else null,
                    gps_longitude = GPSUtils.getLongitude(),
                    gps_latitude = GPSUtils.getLatitude(),
                    input_language = InputMethodUtils.getInputLanguages()
                )
                val info = encryptRiskInfo(riskInfo, kFactor)
                val loginRequest = LoginRequest(
                    oauthType = oauthType,
                    token = token,
                    relogin = relogin,
                    info = info
                )
                val response = RetrofitUtils.dataRepository.login(loginRequest)
                if(response is NetworkResult.Success){
                    response.data?.let { onSuccess(it) }
                }else {
                    onError("Login failed: $response")
                }
            } catch (e: IOException) {
                Log.e("LoginViewModel", "Network error", e)
                onError("Network error: ${e.localizedMessage}")
            } catch (e: HttpException) {
                Log.e("LoginViewModel", "HTTP error", e)
                onError("HTTP error: ${e.localizedMessage}")
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Unexpected error", e)
                onError("Unexpected error: ${e.localizedMessage}")
            }
        }
    }

    fun encryptRiskInfo(riskInfo: RiskInfo, kFactor: String?): String? {
        if (kFactor.isNullOrEmpty()) return null
        return try {
            val json = Json.encodeToString(riskInfo)
            val aesEncrypted = AESUtils.encrypt(json, kFactor) // 128位 AES/ECB/PKCS5Padding
            Base64.encodeToString(
                aesEncrypted.toByteArray(Charsets.UTF_8),
                Base64.URL_SAFE or Base64.NO_WRAP
            )
        } catch (e: Exception) {
            null
        }
    }


    fun getStrategy(
        onSuccess: (StrategyConfig) -> Unit,
        onError: (String) -> Unit
    ) {
        StrategyManager.getStrategy(
            forceRefresh = true,
            onSuccess = {
                GlobalManager.onLoginSuccess()

                UserInfoManager.refreshMyUserInfo()

                // 初始化Agora
                val appId = AppConfigManager.getDecryptedAppConfig()?.items?.find { it.name == "rtck" }?.data?.jsonPrimitive?.contentOrNull
                AgodaUtils.initializeGlobal(CallmeApplication.context, appId)

                ThreadUtils.runOnMain {

                    // 融云--需要主线程，不然会出现Cannot invoke observeForever on a background thread错误
                    val rcKey = AppConfigManager.getConfigValue("rc_app_key")
                    val rcAreaCode = AppConfigManager.getConfigValue("rc_area_code")
                    RongCloudManager.init(CallmeApplication.getInstance(),rcKey,rcAreaCode)

                    onSuccess(it)
                }
            }, onError = {
                ThreadUtils.runOnMain { onError(it) }
            }
        )
    }
} 