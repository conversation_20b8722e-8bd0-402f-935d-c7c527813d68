package com.score.callmetest.ui.mine.blockList

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemFragmentBlocklistBinding
import com.score.callmetest.network.BlockListItem
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class BlockListAdapter(
    private val onUnblockClick: ((BlockListItem) -> Unit)? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_BLOCK = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }
    private val data = mutableListOf<BlockListItem>()
    private var showBottom = false

    override fun getItemCount(): Int = data.size + if (showBottom) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (showBottom && position == data.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_BLOCK
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_BLOCK -> BlockListViewHolder(ItemFragmentBlocklistBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            VIEW_TYPE_BOTTOM -> BottomViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_list_bottom, parent, false))
            else -> throw IllegalArgumentException("Unknown view type: $viewType")// todo 异常类型主动报错，后面可以删除
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is BlockListViewHolder && position < data.size) {
            val item = data[position]
            holder.binding.apply {
                // 昵称
                tvUsername.text = item.nickName ?: "--"
                // 头像
                val avatarUrl = item.avatar ?: ""
                if (avatarUrl.isNotEmpty()) {
                    Glide.with(ivAvatar.context)
                        .load(avatarUrl)
                        .placeholder(R.drawable.placeholder)
                        .error(R.drawable.placeholder)
                        .into(ivAvatar)
                } else {
                    ivAvatar.setImageResource(R.drawable.placeholder)
                }
                // 国家
                tvRegion.text = item.registerCountry ?: "ALL"
                ivFlag.setImageResource(CountryUtils.getIconByEnName(item.registerCountry))

                // 设置背景
                DrawableUtils.setRoundRectBackground(
                    tvUnBlock,
                    ContextCompat.getColor(context,R.color.block_Unblock), 
                    com.score.callmetest.util.DisplayUtils.dp2pxInternal(14f).toFloat()
                )

                // 解除 block 按钮
                tvUnBlock.click {
                    onUnblockClick?.invoke(item)
                }
            }
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }

    fun setData(newData: List<BlockListItem>) {
        data.clear()
        data.addAll(newData)
        notifyDataSetChanged()
    }

    /**
     * 设置是否显示底部项
     */
    fun setShowBottom(show: Boolean) {
        if (showBottom != show) {
            showBottom = show
            notifyDataSetChanged()
        }
    }

    class BlockListViewHolder(val binding: ItemFragmentBlocklistBinding) : RecyclerView.ViewHolder(binding.root)

    class BottomViewHolder(itemView: android.view.View) : RecyclerView.ViewHolder(itemView) {
        private val tvBottomText: android.widget.TextView = itemView.findViewById( R.id.tv_bottom_text)
        fun bind(text: String) {
            tvBottomText.text = text
        }
    }
}