package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spanned
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogCustomBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class BaseCustomDialog(
    context: Context,
    @DrawableRes private val emojiResId: Int? = null,
    private val emojiSvgaResString: String? = null,
    private val title:  Any? = null, // String 或 Spanned
    private val content: Any? = null, // String 或 Spanned
    private val agreeText: String = "Confirm",
    private val agreeBg: Drawable = DrawableUtils.createGradientDrawable(
        colors = GlobalManager.getMainButtonBgGradientColors(),
        radius = DisplayUtils.dp2pxInternalFloat(24f)
    ),
    private val agreeTextColor: Int = Color.WHITE,
    private val cancelText: String = "Cancel",
    private val cancelBg: Drawable = DrawableUtils.createRoundRectDrawable(
        Color.BLACK,
        DisplayUtils.dp2pxInternalFloat(24f)
    ),
    private val cancelTextColor: Int = Color.WHITE,
    private val bgRes: Int = R.drawable.login_dialog_bg,
    private val onAgree: ((Dialog) -> Unit)? = null,
    private val onCancel: ((Dialog) -> Unit)? = null
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    private var binding: DialogCustomBinding = DialogCustomBinding.inflate(LayoutInflater.from(context))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCancelable(false)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        // 点击空白区域可取消
        setCanceledOnTouchOutside(false)

        binding.dialogBg.setBackgroundResource(bgRes)

        // 设置内容左右38dp边距
        val paddingPx = (38 * context.resources.displayMetrics.density).toInt()
        binding.root.setPadding(
            paddingPx,
            binding.root.paddingTop,
            paddingPx,
            binding.root.paddingBottom
        )

        emojiResId?.let {
            binding.emojiSvga.visibility = View.GONE
            binding.emoji.visibility = View.VISIBLE
            binding.emoji.setImageResource(it)
        }
        emojiSvgaResString?.let {
            binding.emojiSvga.visibility = View.VISIBLE
            binding.emoji.visibility = View.GONE
            val parser = SVGAParser(context)
            parser.decodeFromAssets(emojiSvgaResString, object : SVGAParser.ParseCompletion {
                override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                    binding.emojiSvga.setImageDrawable(SVGADrawable(videoItem))
                    binding.emojiSvga.loops = 0 // 无限循环
                    binding.emojiSvga.startAnimation()
                }

                override fun onError() {}
            })
        }


        binding.title.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
        when (title) {
            is Spanned -> binding.title.text = title
            is String -> binding.title.text = title
            else -> {
                binding.title.text = ""
            }
        }
        binding.title.visibility =
            if (binding.title.text.toString().isEmpty()) View.GONE else View.VISIBLE

        binding.content.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
        when (content) {
            is Spanned -> binding.content.text = content
            is String -> binding.content.text = content
            else -> binding.content.text = ""
        }

        binding.content.visibility =
            if (binding.content.text.toString().isEmpty()) View.GONE else View.VISIBLE

        if (agreeText.isNotEmpty()) {
            binding.btnAgree.text = agreeText
            binding.btnAgreeLayout.visibility = View.VISIBLE
        } else {
            binding.btnAgreeLayout.visibility = View.GONE
        }

        if (cancelText.isNotEmpty()) {
            binding.btnCancel.text = cancelText
            binding.btnCancelLayout.visibility = View.VISIBLE
        } else {
            binding.btnCancelLayout.visibility = View.GONE
        }

        binding.btnAgreeLayout.background = agreeBg
        binding.btnAgree.setTextColor(agreeTextColor)
        binding.btnAgree.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));

        binding.btnAgreeLayout.click {
            onAgree?.invoke(this)
            dismiss()
        }

        binding.btnCancelLayout.background = cancelBg
        binding.btnCancel.setTextColor(cancelTextColor)
        binding.btnCancel.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
        binding.btnCancelLayout.click {
            onCancel?.invoke(this)
            dismiss()
        }
    }

    fun getAgreeView() : TextView {
        return binding.btnAgree
    }

    fun getCancelView() : TextView {
        return binding.btnCancel
    }

} 