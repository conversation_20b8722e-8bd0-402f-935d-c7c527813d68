package com.score.callmetest.ui.preview

import android.app.Activity
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.github.chrisbanes.photoview.PhotoView
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.StatusBarUtils

class MultiImagePreviewActivity : Activity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        // 设置全屏沉浸模式
        StatusBarUtils.setFullscreenImmersive(this)
        super.onCreate(savedInstanceState)
        val imageUris = intent.getParcelableArrayListExtra<Uri>("imageUris") ?: arrayListOf()
        val startIndex = intent.getIntExtra("startIndex", 0)
        val viewPager = ViewPager2(this).apply {
            layoutParams = FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            setBackgroundColor(Color.BLACK)
        }
        viewPager.adapter = object : RecyclerView.Adapter<PhotoViewHolder>() {
            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
                val photoView = PhotoView(parent.context).apply {
                    layoutParams = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    setBackgroundColor(Color.BLACK)
                    scaleType = ImageView.ScaleType.FIT_CENTER
                    setOnClickListener { (context as? Activity)?.finish() }
                }
                return PhotoViewHolder(photoView)
            }
            override fun getItemCount() = imageUris.size
            override fun onBindViewHolder(holder: PhotoViewHolder, position: Int) {
                GlideUtils.load(holder.itemView.context, imageUris[position], holder.photoView)
            }
        }
        viewPager.setCurrentItem(startIndex, false)
        val frame = FrameLayout(this)
        frame.addView(viewPager)
        setContentView(frame)
    }
    class PhotoViewHolder(val photoView: PhotoView) : RecyclerView.ViewHolder(photoView)
}