package com.score.callmetest.ui.profile

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Bundle
import android.text.InputFilter
import android.view.inputmethod.InputMethodManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityProfileBinding
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.widget.DatePickerBottomSheet
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ImageUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.SimpleTextWatcher
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
// 移除UCrop导入，使用系统裁剪
import kotlinx.coroutines.launch

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 个人资料编辑页面
 * 功能包括：头像上传、昵称编辑、年龄选择、国家选择、自我介绍编辑、照片管理
 */
class ProfileActivity : BaseActivity<ActivityProfileBinding, ProfileViewModel>() {

    // 照片适配器
    private lateinit var photoAdapter: PhotoAdapter
    // 照片列表
    private val photoList = mutableListOf<Uri>()
    // 选中的头像URI
    private var selectedImageUri: Uri? = null
    // 图片选择器启动器
    private lateinit var imagePickerLauncher: ActivityResultLauncher<Intent>
    // 照片选择器启动器
    private lateinit var photoPickerLauncher: ActivityResultLauncher<Intent>
    // 图片裁剪完成回调
    private var onImageSelectedCallback: ((Uri) -> Unit)? = null

    // 年龄（只做展示用）
    private var age: Int? = null

    /**
     * Activity创建时的初始化
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 注册头像图片选择结果处理器
        imagePickerLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val imageUri = result.data?.data
                if (imageUri != null) {
                    // 直接使用选择的图片，不进行裁剪
                    onImageSelectedCallback?.invoke(imageUri)
                }
            }
        }

        // 注册照片选择结果处理器
        photoPickerLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val imageUri = result.data?.data
                if (imageUri != null && photoList.size < 4) {
                    photoList.add(imageUri)
                    photoAdapter.notifyDataSetChanged()
                    updateAddPhotoButtonVisibility()
                    validateSubmitButton()
                } else if (imageUri != null) {
                    ToastUtils.showToast("Maximum 4 photos allowed")
                }
            }
        }
    }




    override fun getViewBinding(): ActivityProfileBinding {
        return ActivityProfileBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = ProfileViewModel::class.java

    /**
     * 初始化视图
     */
    override fun initView() {
        setupToolbar()
        setupProfileData()
        setupPhotoRecyclerView()
        setupInputFilters()
        viewModel.recordOriginalProfile(UserInfoManager.myUserInfo)

        // 设置完成按钮的阴影背景
        DrawableUtils.setShadowBackground(
            binding.btnDone,
            Color.WHITE,
            DisplayUtils.dp2pxInternal(15f).toFloat(),
            ("#52cce5fd").toColorInt(),
            15f // shadowRadius
        )

        // 让自我介绍输入框支持内部滑动
        binding.etSelfIntroduction.setMovementMethod(android.text.method.ScrollingMovementMethod.getInstance())
        binding.etSelfIntroduction.setOnTouchListener { v, event ->
            v.parent.requestDisallowInterceptTouchEvent(true)
            false
        }
    }

    /**
     * 初始化监听器
     */
    override fun initListener() {
        // 完成按钮点击监听
        binding.btnDone.click {
            if (!validateAllFields() || !viewModel.isProfileChanged()) return@click
            uploadAndSaveAll()
        }

        // 返回按钮点击监听
        binding.ivBack.click {
            finish()
        }

        // 头像选择点击监听
        binding.avatarLayout.click {
            launchImagePicker { uri ->
                selectedImageUri = uri
                GlideUtils.load(this, uri, binding.ivAvatar)
                ToastUtils.showToast(getString(R.string.avatar_updated))
                validateSubmitButton()
            }
        }

        // ID复制按钮点击监听
        binding.btnCopy.click {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            clipboard.setPrimaryClip(ClipData.newPlainText("User ID", binding.tvId.text))
            ToastUtils.showToast(getString(R.string.id_copied))
        }

        // 昵称区域点击监听 - 获取焦点并显示键盘
        binding.nicknameLayout.click {
            binding.etNickname.requestFocus()
            showKeyboard(binding.etNickname)
        }

        // 昵称输入监听器 - 检查字符限制并显示提示
        binding.etNickname.addTextChangedListener(SimpleTextWatcher { text ->
            // 检查是否达到字符限制
            if (text.length >= 20) {
                ToastUtils.showToast(getString(R.string.max_20_chars))
            }
            validateSubmitButton()
        })

        // 自我介绍输入监听器 - 更新字符计数并检查限制
        binding.etSelfIntroduction.addTextChangedListener(SimpleTextWatcher { text ->
            updateSelfIntroCharCount(text)
            // 检查是否达到字符限制
            if (text.length >= 200) {
                ToastUtils.showToast(getString(R.string.max_200_chars))
            }
            validateSubmitButton()
        })

        // 性别点击监听 - 不可修改
        binding.genderLayout.click { ToastUtils.showToast(getString(R.string.gender_cannot_be_changed)) }
        // 年龄点击监听 - 显示生日选择弹窗
        binding.ageLayout.click { showDateOfBirthDialog() }
        // 国家/地区点击监听 - 显示国家选择弹窗
        binding.regionLayout.click { showCountrySelectionDialog() }
    }

    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
    }

    /**
     * 设置个人资料数据
     */
    private fun setupProfileData() {
        val userInfo = UserInfoManager.myUserInfo
        // 加载头像
        GlideUtils.load(this, userInfo?.avatarThumbUrl, binding.ivAvatar)

        binding.apply {
            // 设置用户ID
            tvId.text = userInfo?.userId.toString()
            // 设置昵称
            etNickname.setText(userInfo?.nickname)

            // 设置性别显示
            tvGender.text = if (userInfo?.gender == 1) "Male" else "Female"
            // 计算并显示年龄
            age = getAgeFromBirthday(userInfo?.birthday).toIntOrNull()
            tvAge.text = age?.toString() ?: "23"
            // 设置国家/地区
            tvRegion.text = userInfo?.country ?: "India"
            // 设置自我介绍
            etSelfIntroduction.setText(userInfo?.about ?: "")
            // 更新字符计数
            updateSelfIntroCharCount(etSelfIntroduction.text.toString())
        }
    }

    private fun setupPhotoRecyclerView() {
        // 初始化photoList为mediaList的thumbUrl或mediaUrl
        photoList.clear()
        UserInfoManager.myUserInfo?.mediaList?.forEach { media ->
            val url = media.mediaUrl
            if (!url.isNullOrEmpty()) {
                photoList.add(Uri.parse(url))
            }
        }

        photoAdapter = PhotoAdapter(
            photoList = photoList,
            onDeleteClick = { position ->
                photoList.removeAt(position)
                photoAdapter.notifyDataSetChanged()
                updateAddPhotoButtonVisibility()
                validateSubmitButton()
            },
            onAddClick = {
                if (photoList.size < 4) {
                    launchImagePickerForPhoto()
                }
            }
        )
        // 直接进入多图大图预览
        photoAdapter.setOnPhotoClickListener { _, position ->
            ImageUtils.previewImages(
                activity = this,
                imageUris = photoList,
                startIndex = position
            )
        }
        binding.photoRecyclerview.apply {
            layoutManager =
                LinearLayoutManager(this@ProfileActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = photoAdapter
        }
    }

    private fun updateAddPhotoButtonVisibility() {
        photoAdapter.updateAddButtonVisibility(photoList.size < 4)
    }

    /**
     * 启动头像选择器
     */
    private fun launchImagePicker(onImageCropped: (Uri) -> Unit) {
        AppPermissionManager.checkAndRequestStoragePermission(
            this,
            onGranted = {
                onImageSelectedCallback = onImageCropped
                val intent = Intent(Intent.ACTION_PICK).apply { type = "image/*" }
                imagePickerLauncher.launch(intent)
            },
            onDenied = {
                ToastUtils.showToast(getString(R.string.album_permission_hint_avatar))
            }
        )
    }

    /**
     * 启动照片选择器
     */
    private fun launchImagePickerForPhoto() {
        AppPermissionManager.checkAndRequestStoragePermission(
            this,
            onGranted = {
                val intent = Intent(Intent.ACTION_PICK).apply { type = "image/*" }
                photoPickerLauncher.launch(intent)
            },
            onDenied = {
                ToastUtils.showToast(getString(R.string.album_permission_hint_photo))
            }
        )
    }
    //根据生日求年龄，默认23
    private fun getAgeFromBirthday(birthday: String?): String {
        if (birthday.isNullOrEmpty()) return "23"
        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val birthDate = dateFormat.parse(birthday)
            if (birthDate != null) {
                calculateAge(birthDate).toString()
            } else {
                "23"
            }
        } catch (e: Exception) {
            "23"
        }
    }
    /**
     * 通过生日计算年龄
     */
    private fun calculateAge(birthDate: Date): Int {
        val today = Calendar.getInstance()
        val birthCalendar = Calendar.getInstance().apply { time = birthDate }
        var age = today.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)
        if (today.get(Calendar.DAY_OF_YEAR) < birthCalendar.get(Calendar.DAY_OF_YEAR)) {
            age--
        }
        return age
    }


    /**
     * 展示显示生日弹窗（Age选项）
     * 目前显示的age是通过birthday算出来的。
     */
    private fun showDateOfBirthDialog() {
        val calendar = Calendar.getInstance()
        val currentYear = calendar.get(Calendar.YEAR)
        val maxYear = currentYear - 18

        val birthday = UserInfoManager.myUserInfo?.birthday

        val datePickerBottomSheet = DatePickerBottomSheet(
            maxYear = maxYear,
            onDateSelected = { year, month, day ->
                val selectedDate = Calendar.getInstance().apply {
                    set(year, month - 1, day)
                }
                val calculatedAge = calculateAge(selectedDate.time)
                if (calculatedAge < 18) {
                    ToastUtils.showToast(getString(R.string.age_limit_18))
                    return@DatePickerBottomSheet
                }
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val birthdayStr = dateFormat.format(selectedDate.time)
                age = calculatedAge
                binding.tvAge.text = age.toString()
                viewModel.selectedBirthday = birthdayStr // 只赋值birthday用于上传
                validateSubmitButton()
            },
            birthday = birthday
        )

        datePickerBottomSheet.show(supportFragmentManager, "DatePickerBottomSheet")
    }

    /**
     * 展示选择国家弹窗
     */
    private fun showCountrySelectionDialog() {
        val bottomSheet = com.score.callmetest.ui.widget.CountrySelectBottomSheet(
            context = this,
            selectedCountry = binding.tvRegion.text.toString(),
            onCountrySelected = { selectedCountry ->
                binding.tvRegion.text = selectedCountry
                // 显示国家图标
               // binding.imageRegion.setImageResource(CountryUtils.getIconByEnName(selectedCountry))
                validateSubmitButton()
            }
        )
        bottomSheet.show(supportFragmentManager, "CountrySelectBottomSheet")
    }
    /**
     *验证必须的字段（nickname，birthday，country）
     */
    private fun validateAllFields(): Boolean {
        val nickname = binding.etNickname.text.toString().trim()
        val dateOfBirth = binding.tvAge.text.toString()
        val country = binding.tvRegion.text.toString()
        if (nickname.isEmpty()) {
            ToastUtils.showToast(getString(R.string.nickname_cannot_be_empty))
            return false
        }
        if (dateOfBirth.isEmpty()) {
            ToastUtils.showToast(getString(R.string.birthday_cannot_be_empty))
            return false
        }
        if (country.isEmpty()) {
            ToastUtils.showToast(getString(R.string.country_cannot_be_empty))
            return false
        }
        return true
    }

    private fun syncUiToViewModel() {
        viewModel.selectedAvatarUri = selectedImageUri
        viewModel.selectedNickname = binding.etNickname.text.toString().trim()
        viewModel.selectedGender = if (binding.tvGender.text == "Male") 1 else 0
        // 不再赋值 birthday，birthday 只在选择生日时赋值
        viewModel.selectedCountry = binding.tvRegion.text.toString()
        viewModel.selectedAbout = binding.etSelfIntroduction.text.toString()
        viewModel.selectedMediaUris = photoList.toList()
    }

    private fun validateSubmitButton() {
        syncUiToViewModel()
        val nickname = viewModel.selectedNickname ?: ""
        val dateOfBirth = viewModel.selectedBirthday ?: ""
        val country = viewModel.selectedCountry ?: ""
        val enabled = viewModel.isProfileChanged() && nickname.isNotEmpty() && dateOfBirth.isNotEmpty() && country.isNotEmpty()
        binding.btnDone.isEnabled = enabled
        // 根据enabled动态设置背景和字体颜色
        if (enabled) {
            // 只改字体色
            binding.btnDone.setTextColor("#FF51F1".toColorInt())
            binding.btnDone.doOnPreDraw {
                binding.btnDone.background = DrawableUtils.createRoundRectDrawableWithStroke(
                    fillColor = Color.WHITE,
                    radius = binding.btnDone.height / 2f,
                    strokeColor = "#FFBFE5".toColorInt(),
                    strokeWidth = DisplayUtils.dp2pxInternal(1f)
                )
            }
            // 修改背景色
            (binding.btnDone.background as? GradientDrawable)?.setColor(0xFF7DF7EC.toInt())
        } else {
            binding.btnDone.setTextColor(0xFF4D000000.toInt())
            GlobalManager.setViewRoundBackground(binding.btnDone, Color.WHITE)
        }
    }

    private fun uploadAndSaveAll() {
        syncUiToViewModel()
        LoadingUtils.showLoading(this)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                try {
                    viewModel.uploadAndSaveAll(
                        context = this@ProfileActivity,
                        showToast = {
                            LoadingUtils.dismissLoading()
                            ToastUtils.showToast(it)
                        },
                        onSuccess = {
                            LoadingUtils.dismissLoading()
                            finish()
                        }
                    )
                } catch (e: Exception) {
                    LoadingUtils.dismissLoading()
                }
            }
        }
    }

    /**
     * 设置输入过滤器，防止超出字符限制
     * 优化：使用 LengthFilter 进行硬限制，通过 TextWatcher 监听并显示提示
     */
    private fun setupInputFilters() {
        // 昵称输入过滤器 - 最大20字符（只使用长度过滤器，不在这里显示Toast）
        binding.etNickname.filters = arrayOf(InputFilter.LengthFilter(20))

        // 自我介绍输入过滤器 - 最大200字符（只使用长度过滤器，不在这里显示Toast）
        binding.etSelfIntroduction.filters = arrayOf(InputFilter.LengthFilter(200))

        // 为自我介绍启用复制粘贴功能
        binding.etSelfIntroduction.setTextIsSelectable(true)
        binding.etSelfIntroduction.isLongClickable = true
    }

    /**
     * 更新自我介绍字符计数
     */
    private fun updateSelfIntroCharCount(text: String) {
        binding.tvCharCount.text = "${text.length}/200"
    }

    /**
     * 显示软键盘
     */
    private fun showKeyboard(editText: android.widget.EditText) {
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        editText.requestFocus()
        imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(this, requestCode, permissions, grantResults)
    }
    

}

