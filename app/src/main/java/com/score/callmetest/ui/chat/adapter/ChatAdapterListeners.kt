package com.score.callmetest.ui.chat.adapter

import android.view.View
import com.score.callmetest.entity.ChatMessageEntity

/**
 * 消息涉及到的所有listener
 */
internal data class ChatAdapterListeners(

    // 消息点击监听(text、voice)
    var mOnMessageClickListener: ((ChatMessageEntity, View) -> Unit)? = null,
    // 消息长按监听(text、voice)
    var mOnMessageLongClickListener: ((ChatMessageEntity, View) -> Boolean)? = null,
    // 重发按钮点击监听
    var mOnResendClickListener: ((ChatMessageEntity) -> Unit)? = null,
    // 图片点击监听
    var mOnImageClickListener: ((ChatMessageEntity, View) -> Unit)? = null,
    // 图片长按
    var mOnImageLongClickListener: ((ChatMessageEntity, View) -> Boolean)? = null,
    // 头像点击监听
    var mOnAvatarClickListener: ((ChatMessageEntity) -> Unit)? = null,
    // 翻译按钮
    var mOnTranslateClickListener: ((ChatMessageEntity) -> Unit)? = null,
    // FAQ问题点击监听
    var mOnFaqQuestionClickListener: ((Int, ChatMessageEntity) -> Unit)? = null

)
