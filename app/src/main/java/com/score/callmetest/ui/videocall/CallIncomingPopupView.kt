package com.score.callmetest.ui.videocall

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click

class CallIncomingPopupView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private var onAccept: (() -> Unit)? = null
    private var onReject: (() -> Unit)? = null
    private var onContentClick: (() -> Unit)? = null

    private val avatar: ImageView
    private val nickname: TextView
    private val age: TextView
    private val ageLayout: ViewGroup
    private val country: TextView
    private val acceptBtn: SVGAImageView
    private val rejectBtn: View
    private val bottomShadow: View
    private val contentArea: View
    private val tip: TextView
    private val ageIcon: ImageView

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_call_incoming_popup, this, true)
        avatar = findViewById(R.id.ivAvatar)
        nickname = findViewById(R.id.tvNickname)
        ageLayout = findViewById(R.id.age_layout)
        age = findViewById(R.id.tv_age)
        country = findViewById(R.id.tv_country)
        acceptBtn = findViewById(R.id.btnAccept)
        rejectBtn = findViewById(R.id.btnReject)
        bottomShadow = findViewById(R.id.bottom_shadow)
        contentArea = findViewById(R.id.contentArea)
        tip = findViewById(R.id.tv_tip)
        ageIcon = findViewById(R.id.age_icon)
    }

    fun bind(
        avatarUrl: String?,
        nicknameStr: String?,
        ageStr: String?,
        countryStr: String?,
        price: String = "free",
        onAccept: (() -> Unit)?,
        onReject: (() -> Unit)?,
        onContentClick: (() -> Unit)?
    ) {
        val fullText = "You will be charged $price coins / min."
        val spannable = SpannableString(fullText)
        val start = fullText.indexOf(price)
        val end = start + price.length
        spannable.setSpan(
            ForegroundColorSpan("#2DE314".toColorInt()),
            start, end,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tip.text = spannable
        if (StrategyManager.isReviewPkg()) {
            tip.visibility = View.GONE
        }

        ageIcon.setColorFilter(Color.WHITE)
        GlobalManager.setViewRoundBackground(ageLayout, resources.getColor(R.color.age_color))
        GlobalManager.setViewRoundBackground(country, resources.getColor(R.color.country_color))

        GlideUtils.load(
            view = avatar,
            url = avatarUrl,
            placeholder = R.drawable.placeholder,
            radius = DisplayUtils.dp2pxInternal(18f)
        )
        avatar.doOnPreDraw {
            val size = contentArea.height.coerceAtMost(contentArea.width)
            avatar.layoutParams.width = size
            avatar.layoutParams.height = size
            avatar.requestLayout()
        }

        nickname.text = nicknameStr ?: ""
        age.text = ageStr ?: ""
        country.text = countryStr ?: ""

        bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf(Color.TRANSPARENT, Color.BLACK),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )

        this.onAccept = onAccept
        this.onReject = onReject
        this.onContentClick = onContentClick
        acceptBtn.click { onAccept?.invoke() }
        rejectBtn.click { onReject?.invoke() }
        contentArea.click { onContentClick?.invoke() }
        // 播放SVGA动画
        val parser = SVGAParser(context)
        parser.decodeFromAssets("video_comming.svga", object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                acceptBtn.setImageDrawable(SVGADrawable(videoItem))
                acceptBtn.startAnimation()
            }

            override fun onError() {}
        })

    }
}