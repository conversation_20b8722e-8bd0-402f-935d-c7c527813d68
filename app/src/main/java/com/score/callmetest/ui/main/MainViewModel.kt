package com.score.callmetest.ui.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RcTokenRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MainViewModel : BaseViewModel() {
    /**
     * 融云token
     */
    fun getRongToken(onSuccess: (String?) -> Unit,
                     onError: (Exception) -> Unit){
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO){
                    val request = RcTokenRequest(AppConfigManager.getConfigValue("rc_app_key")?:"")
                    RetrofitUtils.dataRepository.getRongCloudToken(request)
                }
                if(response is NetworkResult.Success){
                    onSuccess(response.data)
                }
            }catch (e: Exception){
                onError(e)
            }
        }
    }

} 