package com.score.callmetest.ui.chat.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemPhotoAlbumBinding
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click

/**
 * 相册图片适配器
 */
class PhotoAlbumAdapter : ListAdapter<String, PhotoAlbumAdapter.ViewHolder>(PhotoDiffCallback()) {

    private var onPhotoClickListener: ((String, Int) -> Unit)? = null

    fun setOnPhotoClickListener(listener: (String, Int) -> Unit) {
        onPhotoClickListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemPhotoAlbumBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position), position)
    }

    inner class ViewHolder(private val binding: ItemPhotoAlbumBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(photoUrl: String, position: Int) {
            // 加载图片
            GlideUtils.load(
                context = binding.ivPhoto.context,
                imageView = binding.ivPhoto,
                url = photoUrl,
                placeholder = R.drawable.image_placeholder,
                error = R.drawable.image_error,
                extraTransform = arrayOf(CenterCrop())
            )

            // 设置点击事件
            ClickUtils.setOnGlobalDebounceClickListener(binding.ivPhoto) {
                onPhotoClickListener?.invoke(photoUrl, position)
            }
        }
    }

    /**
     * 图片差异比较回调
     */
    private class PhotoDiffCallback : DiffUtil.ItemCallback<String>() {
        override fun areItemsTheSame(oldItem: String, newItem: String): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: String, newItem: String): Boolean {
            return oldItem == newItem
        }
    }
} 