package com.score.callmetest.ui.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.score.callmetest.util.ClickUtils
import timber.log.Timber

abstract class BaseFragment<VB : ViewBinding, VM : ViewModel> : Fragment() {
    protected lateinit var binding: VB
    protected lateinit var viewModel: VM

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = getViewBinding(inflater, container)
        viewModel = ViewModelProvider(this)[getViewModelClass()]
        initView()
        initListener()
        initData()
        Timber.tag("dsc--").d("${javaClass.simpleName} onCreateView completed")
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }
    
    override fun onResume() {
        super.onResume()
        // 清除所有点击记录，避免从其他Fragment返回时的异常
        ClickUtils.clearAllClickRecords()
        Timber.tag("dsc--").d("${javaClass.simpleName} onResume called")
    }
    
    /**
     * 获取ViewBinding对象
     * 子类必须实现此方法来返回对应的ViewBinding
     */
    protected abstract fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): VB

    /**
     * 创建ViewModel对象
     * 子类必须实现此方法来创建对应的ViewModel
     */
    protected abstract fun getViewModelClass(): Class<VM>

    /**
     * 初始化视图
     * 子类可以重写此方法来初始化视图
     */
    protected open fun initView() {}

    /**
     * 初始化数据
     * 子类可以重写此方法来初始化数据
     */
    protected open fun initData() {}

    /**
     * 初始化监听器
     * 子类可以重写此方法来初始化监听器
     */
    protected open fun initListener() {}
} 