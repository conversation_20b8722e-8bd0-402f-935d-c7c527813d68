package com.score.callmetest.ui.videocall

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.Html
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCallIncomingBinding
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.network.LiveCallExt2
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.widget.Helper.PhotoPagerHelper
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class CallIncomingFragment(
    val channelName: String,
    val userId: String,
    val avatarUrl: String?,
    val nickname: String?,
    val age: String?,
    val country: String?,
    val freeTip: String?,
) : BaseFragment<FragmentCallIncomingBinding, CallIncomingViewModel>() {
    override fun getViewModelClass() = CallIncomingViewModel::class.java
    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?) =
        FragmentCallIncomingBinding.inflate(inflater, container, false)


    fun getVideoActivity(): VideoCallActivity? {
        return activity as? VideoCallActivity
    }

    override fun initView() {
        // 头像优先用avatarUrl，支持多图轮播
        val imageUrls = mutableListOf<String>()
        if (!avatarUrl.isNullOrEmpty()) {
            imageUrls.add(avatarUrl)
        }
        // 如有mediaList可在此补充
        PhotoPagerHelper.setupPhotoPager(
            viewPager = binding.photoPager,
            imageUrls = imageUrls,
            context = requireContext(),
            onImageClick = { position, urls ->
                // 拦截图片点击，不进入预览大图
            }
        )
        binding.tvNickname.text = nickname ?: ""
        binding.tvAge.text = age ?: ""
        binding.tvCountry.text = country ?: ""
        // 免费提示优先用broadcasterCallDesc
        binding.tvFreeTip.text = if (!freeTip.isNullOrEmpty()) Html.fromHtml(freeTip) else ""

        if (StrategyManager.isReviewPkg()) {
            binding.tvFreeTip.text = ""
        }

        binding.ageLayout.doOnPreDraw {
            binding.ageLayout.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.TRANSPARENT,
                radiusDp = binding.ageLayout.height / 2f,
                strokeColor = resources.getColor(R.color.age_color),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }

        binding.tvCountry.doOnPreDraw {
            binding.tvCountry.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.TRANSPARENT,
                radiusDp = binding.tvCountry.height / 2f,
                strokeColor = resources.getColor(R.color.country_color),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }

        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00080029".toColorInt(), Color.BLACK),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )

        UserInfoManager.getUserInfo(userId) { getUserInfo ->
            getUserInfo?.let { updateUserInfoUI(it) }
        }

        // 播放SVGA动画
        val svgaImageView = binding.btnAccept
        val parser = SVGAParser(requireContext())
        parser.decodeFromAssets("video_comming.svga", object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                svgaImageView.setImageDrawable(SVGADrawable(videoItem))
                svgaImageView.startAnimation()
            }

            override fun onError() {
                // 可选：加载失败处理
            }
        })

        binding.btnAccept.click {
            binding.btnAccept.isEnabled = false
            
            CallIncomingManager.handleAccept(
                onSuccess = { onCallMessage ->
                    binding.btnAccept.isEnabled = true
                    // 接听成功，标记通话开始时间和进入通话状态
                    getVideoActivity()?.getVideoCallViewModel()?.markCallStart()
                    getVideoActivity()?.getVideoCallViewModel()?.hasEnteredOngoing = true
                    VideoCallActivity.startOngoing(
                        requireActivity(),
                        onCallMessage.channelName,
                        onCallMessage.toUserId,
                        onCallMessage.fromUserId
                    )
                },
                onError = {
                    binding.btnAccept.isEnabled = true
                })
        }
        binding.btnReject.click {
            binding.btnReject.isEnabled = false
            
            // 上报拒绝日志
            LogReportManager.reportLiveCallEvent(
                channelName = channelName,
                action = LiveCallAction.HANGUP,
                ext = LiveCallExt.ON_CALL,
                ext2 = LiveCallExt2.HANGUP_BUTTON
            )
            // 用户拒绝通话，保存为REJECTED_CALL
            getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
                videoCallViewModel.saveCallHistory(com.score.callmetest.CallType.REJECTED_CALL)
            }
            CallIncomingManager.handleReject(
                onSuccess = {
                    binding.btnReject.isEnabled = true
                    finishActivity()
                },
                onError = {
                    binding.btnReject.isEnabled = true
                    finishActivity()
                }
            )
        }

        // 监听挂断事件（使用双通道去重）
        DualChannelEventManager.observeOnHangUp(this) { hangUpMessage ->
            // 对方挂断或超时，保存为MISSED_CALL
            getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
                videoCallViewModel.saveCallHistory(com.score.callmetest.CallType.MISSED_CALL)
            }
            CallIncomingManager.handleReject()
            finishActivity()
        }

        // 被呼叫界面进入
        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.ENTER,
            ext = LiveCallExt.ON_CALL
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()

        // 被呼叫界面进入
        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.EXIT,
            ext = LiveCallExt.ON_CALL
        )
        PhotoPagerHelper.cleanup()
    }

    fun finishActivity() {
        try {
            requireActivity().finish()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /**
     * 更新用户信息UI
     */
    private fun updateUserInfoUI(userInfo: com.score.callmetest.network.UserInfo) {
        // 组装图片列表（优先mediaList和头像）
        val realImageUrls = mutableListOf<String>()
        if (!userInfo.avatarThumbUrl.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatarThumbUrl)
        } else if (!userInfo.avatarUrl.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatarUrl)
        } else if (!userInfo.avatar.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatar)
        }
        if (!userInfo.mediaList.isNullOrEmpty()) {
            realImageUrls.addAll(userInfo.mediaList.mapNotNull { it.mediaUrl })
        }
        PhotoPagerHelper.setupPhotoPager(
            viewPager = binding.photoPager,
            imageUrls = realImageUrls,
            context = requireContext(),
            onImageClick = { position, urls ->
                // 拦截图片点击，不进入预览大图
            }
        )

        // 更新其他UI
        binding.tvNickname.text = userInfo.nickname ?: ""
        binding.tvAge.text = userInfo.age?.toString() ?: ""
        binding.tvCountry.text = userInfo.country ?: ""
    }
} 