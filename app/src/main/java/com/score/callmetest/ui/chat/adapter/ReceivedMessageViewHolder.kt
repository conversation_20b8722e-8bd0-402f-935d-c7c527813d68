package com.score.callmetest.ui.chat.adapter

import android.os.Build
import android.text.Html
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.text.method.LinkMovementMethod
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageLeftBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageType
import com.score.callmetest.entity.isSysService
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.TranslateManager
import com.score.callmetest.network.FAQInfoList
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 接收消息ViewHolder
 */
internal class ReceivedMessageViewHolder(
    private val binding: ItemChatMessageLeftBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) :
    RecyclerView.ViewHolder(binding.root), MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null

    /**
     * 是否正在翻译。。。
     */
    private val mIsTransIng = AtomicBoolean(false)

    // 事件
    init {
        // 消息点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvMessage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageClickListener?.invoke(message, binding.root)
            }
        }

        // image点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivImage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnImageClickListener?.invoke(message, binding.root)
            }
        }

        // 消息长按
        binding.tvMessage.setOnLongClickListener {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageLongClickListener?.invoke(message, binding.root)
                    ?: false
            } ?: false
        }

        // image长按
        binding.ivImage.setOnLongClickListener {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnImageLongClickListener?.invoke(message, binding.root)
                    ?: false
            } ?: false
        }

        // 头像
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnAvatarClickListener?.invoke(message)
            }
        }

        // 翻译按钮点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvTranslate) {
            mCurrentMessage?.let { message ->
                if (mIsTransIng.get()) {
                    // 正在翻译---点击无效
                    Timber.d("正在翻译---点击无效")
                    return@let
                }
                // 通知外部已经开始翻译
                mChatMessageListeners.mOnTranslateClickListener?.invoke(message)
                translateMessage()
            }
        }

    }

    private fun translateMessage() {
        showTranslateLoading(true)
        // 翻译前期准备
        val needTranslate = binding.tvMessage.text.toString()
        val targetLang = LanguageUtils.getAppLanguage(binding.root.context)
        // 内部有分配线程执行翻译
        TranslateManager.translate(needTranslate, targetLang) { newTxt ->
            ThreadUtils.runOnMain {
                showTranslateLoading(false)
                if (newTxt == null) {
                    // 翻译失败
                    Timber.d("翻译失败")
                    showTranslateButton(true)
                    return@runOnMain
                }
                // 翻译成功
                Timber.d("翻译成功--$needTranslate-->$newTxt")
                showTranslateResult(newTxt)
            }
        }
    }

    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message
        // 显示消息内容
        binding.tvMessage.text = message.content
        binding.tvOriginMessage.text = message.content
        // 暂时不需要显示时间
//        binding.tvTime.text = message.getFormattedTime()

        if (message.isSysService()) {
            // 客服账号
            GlideUtils.load(
                view = binding.ivAvatar,
                url = R.drawable.customer_service,
                placeholder = R.drawable.customer_service,
                isCircle = true
            )
        } else {
            // 加载头像
            GlideUtils.load(
                view =binding.ivAvatar,
                url = message.senderAvatar,
                placeholder = R.drawable.placeholder,
                error = R.drawable.placeholder,
                isCircle = true
            )
        }

        // 根据消息类型显示不同内容
        when (message.messageType) {
            MessageType.TEXT -> {
                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE

                // 根据消息内容判断是否显示翻译按钮
                if (message.content.isNotEmpty()) {
                    showTranslateButton(true)
                    // 检查是否开启了自动翻译
                    if(message.isAutoTrans){
                        binding.tvMessage.post { translateMessage() }
                    }

                } else {
                    showTranslateButton(false)
                }
                // 其他消息类型视图处理...
            }

            MessageType.IMAGE -> {
                binding.tvMessage.visibility = View.GONE
                binding.ivImage.visibility = View.VISIBLE

                showTranslateButton(false)

                val imgUri = message.thumbUri ?: message.mediaLocalUri
                // 加载图片
                GlideUtils.load(
                    view =binding.ivImage,
                    url = message.mediaUri,
                    placeholder = R.drawable.image_placeholder,
                    error = R.drawable.image_error,
                )
            }

            MessageType.LINK -> {
                // text
                val content = message.content
                val spanned = SpannableString(content)

                // 应用点击事件和样式
                spanned.setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(binding.root.context, R.color.link_color)),
                    0,
                    content.length,
                    SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                // 添加下划线样式（备用）
                spanned.setSpan(
                       UnderlineSpan(),
                       0,
                       content.length,
                       Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                   )

                binding.tvMessage.text = spanned
                // 这里不需要跳转
//                binding.tvMessage.movementMethod = LinkMovementMethod.getInstance()  // 必须设置，否则链接不可点击
//                binding.tvMessage.setLinkTextColor(Color.BLUE) // 在xml中设置了

                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                showTranslateButton(false)
            }

            MessageType.SYSTEM -> {
                // FAQ消息：构建带超链接的文本
                val faqSpannableString = createFaqSpannableString(message)
                binding.tvMessage.text = faqSpannableString
                binding.tvMessage.movementMethod = LinkMovementMethod.getInstance()
                binding.tvMessage.setOnClickListener(null)
                binding.tvMessage.setOnLongClickListener(null)
                binding.tvMessage.isClickable = false
                binding.tvMessage.isLongClickable = false

                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                showTranslateButton(false)
            }

            else -> {
                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                // 其他消息类型暂不处理，可根据需求扩展
            }
        }

    }

    /**
     * 创建FAQ消息的SpannableString，将问题设置为超链接样式
     * @param message FAQ消息实体
     * @return 带有超链接的SpannableString
     */
    private fun createFaqSpannableString(message: ChatMessageEntity): SpannableString {
        val content = message.content
        val spannableString = SpannableString(content)

        try {
            // 解析FAQ信息
            val faqInfoList = if (!message.extra.isNullOrEmpty()) {
                try {
                    Gson().fromJson(message.extra, FAQInfoList::class.java)
                } catch (e: Exception) {
                    Timber.e(e, "Failed to parse FAQ info from extra")
                    null
                }
            } else null

            // 为每个问题设置超链接样式和点击事件
            faqInfoList?.faqInfoList?.forEach { faqInfo ->
                val question = faqInfo.question
                val startIndex = content.indexOf(question)

                if (startIndex != -1) {
                    val endIndex = startIndex + question.length

                    // 创建点击事件
                    val clickableSpan = object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            // 触发FAQ问题点击事件
                            mChatMessageListeners.mOnFaqQuestionClickListener?.invoke(faqInfo.code, message)
                        }

                        override fun updateDrawState(ds: android.text.TextPaint) {
                            super.updateDrawState(ds)
                            // 设置超链接样式：颜色、下划线
                            ds.color = ContextCompat.getColor(binding.root.context, R.color.link_color)
                            ds.isUnderlineText = false
                        }
                    }

                    // 应用点击事件和样式
                    spannableString.setSpan(
                        clickableSpan,
                        startIndex,
                        endIndex,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )

                    // 添加下划线样式（备用）
                    // 暂时不需要下划线
                 /*   spannableString.setSpan(
                        UnderlineSpan(),
                        startIndex,
                        endIndex,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )*/
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to create FAQ spannable string")
        }

        return spannableString
    }


    private fun showTranslateButton(show: Boolean) {
        binding.tvTranslate.visibility = if (show) View.VISIBLE else View.GONE
        binding.progressTranslate.visibility = View.GONE
        binding.translateParent.visibility = View.GONE
    }

    private fun showTranslateLoading(show: Boolean) {
        binding.tvTranslate.visibility = if (show) View.VISIBLE else View.GONE
        binding.progressTranslate.visibility = if (show) View.VISIBLE else View.GONE
        mIsTransIng.set(show)
    }

    private fun showTranslateResult(text: String) {
        binding.tvTranslate.visibility = View.GONE
        binding.progressTranslate.visibility = View.GONE
        binding.tvMessage.visibility = View.INVISIBLE
        binding.translateParent.visibility = View.VISIBLE
        binding.tvTranslateContent.text = text
    }
}