package com.score.callmetest.ui.main

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.network.GoodsInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CoinStoreViewModel : ViewModel() {
    private val _goodsList = MutableLiveData<List<GoodsInfo>>()
    val goodsList: LiveData<List<GoodsInfo>> = _goodsList

    fun loadGoods() {
        viewModelScope.launch {
            val cachedGoods = GoodsManager.getCachedAllGoods()
            if (cachedGoods.isNotEmpty()) {
                _goodsList.value = cachedGoods
            } else {
                val goodsList = GoodsManager.getAllGoods()
                _goodsList.value = goodsList

            }
        }
    }
} 