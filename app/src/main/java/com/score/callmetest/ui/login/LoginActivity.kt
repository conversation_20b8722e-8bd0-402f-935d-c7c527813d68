package com.score.callmetest.ui.login

import android.content.Intent
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.Toast
import com.score.callmetest.databinding.ActivityLoginBinding
import com.score.callmetest.ui.base.BaseActivity
import android.app.AlertDialog
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.style.UnderlineSpan
import com.score.callmetest.CallmeApplication
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.R
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.util.click
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive
import com.score.callmetest.ui.widget.BaseCustomDialog
import android.text.Html
import android.widget.TextView
import java.io.File
import java.io.FileOutputStream
import android.util.Log
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.MediaItem
import androidx.media3.ui.PlayerView
import android.net.Uri
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.ui.widget.DebugDeviceIdDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import timber.log.Timber

class LoginActivity : BaseActivity<ActivityLoginBinding, LoginViewModel>() {
    override fun getViewBinding(): ActivityLoginBinding {
        return ActivityLoginBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = LoginViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        if (AppPermissionManager.shouldShowFirstLaunchPermissionDialog(this)) {
            AppPermissionManager.showFirstLaunchPermissionDialog(
                this,
                onGranted = { /* 可选：权限授权后操作 */ },
                onDenied = { /* 可选：权限拒绝后操作 */ }
            )
        }
        super.onCreate(savedInstanceState)
    }

    override fun initView() {
        // Splash逻辑已移至SplashActivity

        binding.loginButton.doOnPreDraw {
            binding.loginButton.background = DrawableUtils.createGradientDrawable(
                colors = GlobalManager.getMainButtonBgGradientColors(),
                radius = binding.loginButton.height / 2f
            )
        }

        binding.loginButton.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));

        // 播放视频背景
        // fixme: 第一版为了过审不做动画
//        playLoginBgVideo()

        // 设置条款文本
        setupTermsText()
    }

    private var exoPlayer: ExoPlayer? = null

    private fun playLoginBgVideo() {
        val playerView = binding.gifBackground
        val player = ExoPlayer.Builder(this).build()
        exoPlayer = player
        playerView.player = player
        val mediaItem = MediaItem.fromUri("asset:///login_bg.mp4")
        player.setMediaItem(mediaItem)
        player.repeatMode = ExoPlayer.REPEAT_MODE_ALL
        player.volume = 0f
        player.prepare()
        player.playWhenReady = true
    }

    override fun onResume() {
        super.onResume()
        exoPlayer?.playWhenReady = true
    }

    override fun onPause() {
        super.onPause()
        exoPlayer?.playWhenReady = false
    }

    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
        exoPlayer = null
    }

    private fun setupTermsText() {
        val fullText = "By using our App you agree with our User Agreement and Privacy Policy"
        val spannableString = SpannableString(fullText)

        // Terms & Conditions 点击事件
        val termsClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                // 跳转到用户协议页面
                ActivityUtils.startActivity(this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", "https://rice.callmeso.com/CallMe-User-Agreement.html")
                        putString("title", "User Agreement")
                    }
                    ,false)
            }
            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = Color.WHITE
                ds.isUnderlineText = true
            }
        }

        // Privacy Policy 点击事件
        val privacyClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                // 跳转到隐私政策页面
                ActivityUtils.startActivity(this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", "https://rice.callmeso.com/CallMe-Privacy-Policy.html")
                        putString("title", "Privacy Policy")
                    }
                    ,false)
            }
            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = Color.WHITE
                ds.isUnderlineText = true
            }
        }

        // 设置点击区域
        val termsStart = fullText.indexOf("User Agreement")
        val termsEnd = termsStart + "User Agreement".length
        val privacyStart = fullText.indexOf("Privacy Policy")
        val privacyEnd = privacyStart + "Privacy Policy".length

        spannableString.setSpan(
            termsClickableSpan,
            termsStart,
            termsEnd,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            privacyClickableSpan,
            privacyStart,
            privacyEnd,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        binding.termsText.text = spannableString
        binding.termsText.movementMethod = LinkMovementMethod.getInstance()
    }


    override fun initListener() {
        // 登录按钮
        binding.loginButton.click {
            if (binding.termsCheckBox.isChecked) {
                doLogin(DeviceUtils.getAndroidId())
            } else {
                showTermsDialog {
                    doLogin(DeviceUtils.getAndroidId())
                }
            }
        }

        // Google登录按钮
        binding.googleLoginButton.click {
            if (binding.termsCheckBox.isChecked) {
                // 显示loading遮罩
                LoadingUtils.showLoading(this)

                // TODO: 实现Google登录
                // 模拟Google登录过程
                binding.googleLoginButton.postDelayed({
                    LoadingUtils.dismissLoading()
//                    ToastUtils.showToast("Google Login clicked", Toast.LENGTH_SHORT)
                }, 2000) // 模拟2秒的登录过程
            } else {
                ToastUtils.showShortToast(getString(R.string.check_terms_and_conditions))
//                ToastUtils.showToast(
//                    "Please agree to the Terms & Conditions and Privacy Policy",
//                    Toast.LENGTH_SHORT
//                )
            }
        }

        //Todo： 双击后取随机android  id登陆，方便研发调试和后期测试
        binding.callmeTitle.apply {
            var lastClickTime = 0L
            var clickCount = 0
            setOnClickListener {
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastClickTime < 500) { // 双击间隔500ms
                    clickCount++
                    if (clickCount >= 2) {
                        showDebugDeviceIdDialog()
                        clickCount = 0
                    }
                } else {
                    clickCount = 1
                }
                lastClickTime = currentTime
            }
        }


    }

    //todo 双击logo显示调试弹窗
    /**
     * 显示调试设备ID设置弹窗
     */
    private fun showDebugDeviceIdDialog() {
        val dialog = DebugDeviceIdDialog(
            context = this,
            onConfirm = {
                // 弹窗确认后的回调，可以在这里做一些额外的处理
                // 比如刷新UI显示当前使用的设备ID等
            }
        )
        dialog.show()
    }

    private fun showTermsDialog(onAgree: () -> Unit) {
        val fullText = "We hope you have a great experience with our app.  By using our app, you are agreeing to our User Agreement and Privacy Policy."
        val spannableString = SpannableString(fullText)
        val termsStart = fullText.indexOf("User Agreement")
        val termsEnd = termsStart + "User Agreement".length
        val privacyStart = fullText.indexOf("Privacy Policy")
        val privacyEnd = privacyStart + "Privacy Policy".length

        val termsClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                ActivityUtils.startActivity(
                    this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", "https://rice.callmeso.com/CallMe-User-Agreement.html")
                        putString("title", "User Agreement")
                    }, false)

            }

            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = 0xFF808080.toInt() // #808080
                ds.isUnderlineText = true
            }
        }
        val privacyClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {

                ActivityUtils.startActivity(
                    this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", "https://rice.callmeso.com/CallMe-Privacy-Policy.html")
                        putString("title", "Privacy Policy")
                    }, false)
            }
            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = 0xFF808080.toInt() // #808080
                ds.isUnderlineText = true
            }
        }
        val underlineSpan = UnderlineSpan()
        spannableString.setSpan(
            termsClickableSpan,
            termsStart,
            termsEnd,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            underlineSpan,
            termsStart,
            termsEnd,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            privacyClickableSpan,
            privacyStart,
            privacyEnd,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            underlineSpan,
            privacyStart,
            privacyEnd,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        val dialog = BaseCustomDialog(
            context = this,
            emojiResId = R.drawable.emoji_laugh,
            title = "Thank you for taking the time to review these Terms",
            content = spannableString,
            agreeText = "Agree",
            cancelText = "Cancel",
            onAgree = {
                binding.termsCheckBox.isChecked = true
                onAgree()
            },
            onCancel = {}
        )

        dialog.show()

        // 支持富文本点击
        dialog.findViewById<TextView>(R.id.content)?.movementMethod = android.text.method.LinkMovementMethod.getInstance()
    }

    private fun doLogin(deviceId: String) {
        // 显示loading遮罩
        LoadingUtils.showLoading(this)

        viewModel.login(
            token = deviceId,
            onSuccess = { data ->
                if (data.token != null) {
                    if (data.userInfo != null) {
                        UserInfoManager.updateMyUserInfo(data.userInfo)
                    }
                    viewModel.saveLoginData(data.token, false, "guest")


                    viewModel.getStrategy(
                        onSuccess = {
                            LoadingUtils.dismissLoading()
                            // 检查是否需要显示首次启动权限弹窗
                            if (AppPermissionManager.shouldShowFirstLaunchPermissionDialog(this)) {
                                AppPermissionManager.showFirstLaunchPermissionDialog(
                                    this,
                                    onGranted = {
                                        // 权限授权成功，跳转到首页
                                        startActivity(Intent(this, MainActivity::class.java))
                                        finish()
                                    },
                                    onDenied = {
                                        // 权限被拒绝，仍然跳转到首页
                                        startActivity(Intent(this, MainActivity::class.java))
                                        finish()
                                    }
                                )
                            } else {
                                // 跳转到首页
                                startActivity(Intent(this, MainActivity::class.java))
                                finish()
                            }
                        },
                        onError = {
                            // 隐藏loading遮罩
                            LoadingUtils.dismissLoading()
//                            ToastUtils.showToast(it ?: "Login failed", Toast.LENGTH_SHORT)
                            ToastUtils.showShortToast(CallmeApplication.context.getString(R.string.login_error))
                        }
                    )
                } else {
                    // 隐藏loading遮罩
                    LoadingUtils.dismissLoading()
//                    ToastUtils.showToast("Login failed", Toast.LENGTH_SHORT)
                    ToastUtils.showShortToast(CallmeApplication.context.getString(R.string.login_error))
                }
            },
            onError = { msg ->
                // 隐藏loading遮罩
                LoadingUtils.dismissLoading()
                Timber.e(msg)
                ToastUtils.showToast(msg, Toast.LENGTH_SHORT)
            }
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(this, requestCode, permissions, grantResults)
    }

} 