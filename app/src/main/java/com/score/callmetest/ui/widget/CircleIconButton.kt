package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.content.ContextCompat
import com.score.callmetest.R
import androidx.core.content.withStyledAttributes
import androidx.core.graphics.withClip

class CircleIconButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AlphaImageView(context, attrs, defStyleAttr) {

    private var normalAlpha = 1.0f
    private var pressedAlpha = 0.5f
    private var disabledAlpha = 0.2f
    private val clipPath = Path()
    private var viewSize = 0

    init {
        isClickable = true
        isFocusable = true
        background = ContextCompat.getDrawable(context, R.drawable.round_button_bg)
        alpha = normalAlpha
        // 可通过xml自定义icon和背景色
        attrs?.let {
            context.withStyledAttributes(it, R.styleable.RoundIconButton) {
                val icon = getDrawable(R.styleable.RoundIconButton_iconSrc)
                val bgColor = getColor(
                    R.styleable.RoundIconButton_bgColor,
                    ContextCompat.getColor(context, android.R.color.white)
                )
                setImageDrawable(icon)
                background?.setTint(bgColor)
            }
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        alpha = if (enabled) normalAlpha else disabledAlpha
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> alpha = pressedAlpha
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> alpha = normalAlpha
        }
        return super.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewSize = w.coerceAtMost(h)
        val radius = viewSize / 2f
        clipPath.reset()
        clipPath.addCircle(w / 2f, h / 2f, radius, Path.Direction.CW)
        clipPath.close()
    }

    override fun onDraw(canvas: Canvas) {
        canvas.withClip(clipPath) {
            super.onDraw(this)
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 保证宽高一致，取最小值，始终为正圆
        val size = widthMeasureSpec.coerceAtMost(heightMeasureSpec)
        super.onMeasure(size, size)
    }
} 