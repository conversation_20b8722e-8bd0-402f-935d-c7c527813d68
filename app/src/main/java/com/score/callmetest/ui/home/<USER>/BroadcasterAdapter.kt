package com.score.callmetest.ui.home.adapter

import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.provider.CallLog
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.AlphaSVGAImageView
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import timber.log.Timber

class BroadcasterAdapter :
    ListAdapter<BroadcasterModel, BroadcasterAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_broadcaster, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int
    ) {
        holder.bind(this, getItem(position), position)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty() && payloads[0] is String && payloads[0] == "status") {
            val broadcaster = getItem(position)
            // 删除对状态指示器的更新
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        private val avatar: ImageView = view.findViewById(R.id.avatar)

        // 删除状态指示器的引用
        private val nickname: TextView = view.findViewById(R.id.nickname)
        private val language: TextView = view.findViewById(R.id.broadcaster_tv_language)
        private val statusLayout: ViewGroup = view.findViewById(R.id.status_layout)
        private val statusIndicator: View = view.findViewById(R.id.status_indicator)
        private val statusText: TextView = view.findViewById(R.id.status_text)
        private val btnVideoSvga: AlphaSVGAImageView = view.findViewById(R.id.btn_video_svga)
        private val btnVideoGray: ImageView = view.findViewById(R.id.btn_video_gray)
        private val languageLayout: LinearLayout =
            itemView.findViewById(R.id.item_broadcaster_language)
        private val bgMask: View = itemView.findViewById(R.id.bg_mask)


        fun bind(adapter: BroadcasterAdapter, broadcaster: BroadcasterModel, itemPosition: Int) {
            bgMask.background = DrawableUtils.createGradientDrawable(
                colors = intArrayOf(Color.TRANSPARENT, "#881B1C20".toColorInt()),
                orientation = GradientDrawable.Orientation.TOP_BOTTOM
            )
            // 加载头像 - 优先使用avatarThumbUrl，如果没有则使用avatar
            Glide.with(avatar)
                .load(broadcaster.avatarThumbUrl ?: broadcaster.avatar)
                .placeholder(R.drawable.placeholder)
                .error(R.drawable.placeholder)
                .into(avatar)

            // 设置文本信息
            nickname.text = broadcaster.nickname

            language.text = broadcaster.analysisLanguage?.uppercase() ?: ""

            languageLayout.visibility = View.GONE
            GlobalManager.setViewRoundBackground(
                languageLayout,
                "#6e000000".toColorInt()
            )

            // item点击事件
            itemView.click {
                val context = itemView.context
                val intent = Intent(context, BroadcasterDetailActivity::class.java)
                intent.putExtra("broadcaster_model", broadcaster)
                context.startActivity(intent)
            }

            GlobalManager.setViewRoundBackground(statusLayout, "#33000000".toColorInt())
            GlobalManager.setViewRoundBackground(
                statusIndicator, GlobalManager.getStatusColor(broadcaster.status)
            )
            statusText.text = broadcaster.status


            if (StrategyManager.isReviewPkg()) {
                if (broadcaster?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(broadcaster.userId)) {
                    broadcaster.status = CallStatus.ONLINE
                } else {
                    broadcaster?.status = GlobalManager.getReviewOtherStatus(broadcaster.userId)
                }
            }

            if (broadcaster.status == CallStatus.ONLINE || broadcaster.status == CallStatus.AVAILABLE) {
                btnVideoSvga.visibility = View.VISIBLE
                btnVideoGray.visibility = View.GONE
                btnVideoSvga.isClickable = true
                btnVideoSvga.click {
                    val activity = itemView.context as? androidx.appcompat.app.AppCompatActivity
                    if (activity == null) {
//                        ToastUtils.showToast("页面异常，无法发起通话")
                        return@click
                    }
                    // 非审核模式下先判断金币是否足够
                    if (!StrategyManager.isReviewPkg()) {
                        val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
                        val unitPrice = broadcaster.callCoins ?: 0
                        if (availableCoins < unitPrice) {
                            CoinRechargeDialog().show(
                                activity.supportFragmentManager,
                                "coin_recharge"
                            )
                            return@click
                        }
                    }

                    // 检查网络连接
                    if (!SocketManager.instance.isConnected()) {
//                        ToastUtils.showToast("Socket service is offline")
                        Timber.d("Socket service is offline")
                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.net_error_and_try_again))
                        return@click
                    }
                    // 3. 金币和网络检查通过后，再检查权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        activity,
                        onGranted = {
                            // 权限授权成功，再次确认在线状态
                            LoadingUtils.showLoading(activity)
                            UserInfoManager.loadOnlineStatus(
                                scope = CoroutineScope(Dispatchers.IO),
                                userId = broadcaster.userId,
                                callback = { status, error ->
                                    ThreadUtils.runOnMain {
                                        LoadingUtils.dismissLoading()
                                        if (error == null && status != null) {
                                            status.toString().logAsTag(this.javaClass.name + "status: ")
                                            broadcaster.status = status
                                            adapter.notifyItemChanged(itemPosition)
                                            when(status) {
                                                CallStatus.ONLINE, CallStatus.AVAILABLE -> {
                                                    VideoCallActivity.startOutgoing(
                                                        context = activity,
                                                        userId = broadcaster.userId,
                                                        avatarUrl = broadcaster.avatarThumbUrl.toString(),
                                                        nickname = broadcaster.nickname.toString(),
                                                        age = broadcaster.age.toString(),
                                                        country = broadcaster.country.toString(),
                                                        unitPrice = broadcaster.unit.toString()
                                                    )
                                                }
                                                else -> {
//                                                    ToastUtils.showToast("The User is not available now")
                                                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.user_not_available))
                                                }
                                            }
                                        }
                                    }
                                }
                            )
                        },
                        onDenied = {
                            AppPermissionManager.incrementPermissionCheckCount()
                            if (AppPermissionManager.shouldShowPermissionGuide(activity)) {
                                ToastUtils.showToast(CallmeApplication.context.getString(R.string.enable_camera_microphone_permission_in_settings))
                            } else {
                                ToastUtils.showToast(CallmeApplication.context.getString(R.string.camera_microphone_permission_required))
                            }
                        }
                    )
                }

                // 播放SVGA动画
                val context = btnVideoSvga.context
                val parser = com.opensource.svgaplayer.SVGAParser(context)
                parser.decodeFromAssets(
                    "btn_broadcaster.svga",
                    object : com.opensource.svgaplayer.SVGAParser.ParseCompletion {
                        override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                            btnVideoSvga.setImageDrawable(
                                com.opensource.svgaplayer.SVGADrawable(
                                    videoItem
                                )
                            )
                            btnVideoSvga.loops = 0
                            btnVideoSvga.startAnimation()
                        }

                        override fun onError() {}
                    })
            } else {
                btnVideoSvga.visibility = View.GONE
                btnVideoGray.visibility = View.VISIBLE
                btnVideoGray.isClickable = false
            }


            if (StrategyManager.isReviewPkg()) {
//                btnVideoGray.visibility = View.GONE
//                btnVideoSvga.visibility = View.GONE
            }
        }
    }

    private class DiffCallback : DiffUtil.ItemCallback<BroadcasterModel>() {
        override fun areItemsTheSame(
            oldItem: BroadcasterModel,
            newItem: BroadcasterModel
        ): Boolean {
            return oldItem.userId == newItem.userId
        }

        override fun areContentsTheSame(
            oldItem: BroadcasterModel,
            newItem: BroadcasterModel
        ): Boolean {
            return oldItem == newItem
        }
    }

    fun updateStatus(updatedList: List<BroadcasterModel>) {
        // 以当前列表为基础，生成新列表
        val newList = currentList.map { current ->
            val updated = updatedList.find { it.userId == current.userId }
            if (updated != null && current.status != updated.status) {
                current.copy(status = updated.status)
            } else {
                current
            }
        }
        submitList(newList)
    }
}