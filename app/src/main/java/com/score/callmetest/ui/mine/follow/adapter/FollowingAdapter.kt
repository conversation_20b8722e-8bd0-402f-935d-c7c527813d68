package com.score.callmetest.ui.mine.follow.adapter

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.R
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import de.hdodenhof.circleimageview.CircleImageView

class FollowingAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_FOLLOWING = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }
    private val followings = mutableListOf<FollowModel>()
    private var showBottom: Boolean = true

    fun setShowBottom(show: Boolean) {
        if (this.showBottom != show) {
            this.showBottom = show
            notifyDataSetChanged()
        }
    }

    fun setData(newFollowings: List<FollowModel>) {
        followings.clear()
        followings.addAll(newFollowings)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_FOLLOWING -> ViewHolder(
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_fragment_following, parent, false
                )
            )
            VIEW_TYPE_BOTTOM -> BottomViewHolder(
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_list_bottom, parent, false
                )
            )
            // todo 异常类型主动报错，后面可以删除
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder && position < followings.size) {
            val followModel = followings[position]
            // 昵称
            holder.tvUsername.text = followModel.nickname ?: ""
            // 国家/地区
            val bgColor = ContextCompat.getColor(context, R.color.follow_region)
            val radiusDp = 7f
            holder.tvRegion.background = DrawableUtils.createRoundRectDrawableDp(bgColor, radiusDp)
            holder.tvRegion.text = followModel.country ?: ""
            // 头像
            val avatarUrl = followModel.avatarUrl ?:followModel.avatarThumbUrl ?: ""
            if (avatarUrl.isNotEmpty()) {
                Glide.with(holder.ivAvatar.context)
                    .load(avatarUrl)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .into(holder.ivAvatar)
            } else {
                holder.ivAvatar.setImageResource(R.drawable.placeholder)
            }

            // 根据在线状态设置视频通话图标
            if (followModel.onlineStatus == CallStatus.ONLINE) {
                holder.ivVideoCall.setImageResource(R.drawable.call_video)
            } else {
                holder.ivVideoCall.setImageResource(R.drawable.video_follow)
            }
            // 视频通话图标点击事件
            holder.ivVideoCall.click {
                if (followModel.onlineStatus == CallStatus.ONLINE) {
                    val activity = holder.itemView.context as? androidx.appcompat.app.AppCompatActivity
                    if (activity == null) {
                        ToastUtils.showToast(context.getString(R.string.page_error_unable_to_initiate_call))
                        return@click
                    }

                    // 1. 先检查金币是否足够
                    val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
                    val unitPrice = followModel.unitPrice ?: 0
                    if (availableCoins < unitPrice) {
                        CoinRechargeDialog().show(activity.supportFragmentManager, "coin_recharge")
                        return@click
                    }

                    // 2. 检查网络连接
                    if (!com.score.callmetest.manager.SocketManager.instance.isConnected()) {
                        ToastUtils.showToast(context.getString(R.string.long_connection_network_offline))
                        return@click
                    }

                    // 3. 金币和网络都通过后，再请求权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        activity,
                        onGranted = {
                            // 权限授权成功，直接发起通话
                            VideoCallActivity.startOutgoing(
                                context = activity,
                                userId = followModel.userId,
                                avatarUrl = followModel.avatarThumbUrl.toString(),
                                nickname = followModel.nickname.toString(),
                                age = followModel.age.toString(),
                                country = followModel.country.toString(),
                                unitPrice = followModel.unitPrice.toString()
                            )
                        },
                        onDenied = {
                            AppPermissionManager.incrementPermissionCheckCount()
                            if (AppPermissionManager.shouldShowPermissionGuide(activity)) {
                                ToastUtils.showToast(context.getString(R.string.enable_camera_microphone_permission_in_settings))
                            } else {
                                ToastUtils.showToast(context.getString(R.string.camera_microphone_permission_required))
                            }
                        }
                    )
                } else {
                    ToastUtils.showToast(context.getString(R.string.user_offline_unable_to_call))
                }
            }

            // item点击事件
            holder.itemView.click {
                val position = holder.adapterPosition
                if (position != RecyclerView.NO_POSITION && position < followings.size) {
                    val followModel = followings[position]
                    val broadcasterModel = followModelToBroadcasterModel(followModel)
                    val context = holder.itemView.context
                    val intent = Intent(context, BroadcasterDetailActivity::class.java)
                    intent.putExtra("broadcaster_model", broadcasterModel)
                    context.startActivity(intent)
                }
            }
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }

    override fun getItemCount(): Int = followings.size + if (showBottom) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (showBottom && position == followings.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_FOLLOWING
    }

    private fun followModelToBroadcasterModel(followModel: FollowModel): BroadcasterModel {
        return BroadcasterModel(
            userId = followModel.userId,
            nickname = followModel.nickname,
            avatar = followModel.avatar,
            avatarMapPath = null, // FollowModel没有，填null
            gender = followModel.gender,
            age = followModel.age,
            country = followModel.country,
            status = followModel.onlineStatus ?: CallStatus.OFFLINE,
            callCoins = followModel.unitPrice,
            unit = "min", // FollowModel没有，默认"min"
            videoMapPaths = null, // FollowModel没有，填null
            imageMapPaths = null, // FollowModel没有，填null
            followNum = null, // FollowModel没有，填null
            isFriend = followModel.mutualFlow,
            isMultiple = null, // FollowModel没有，填null
            about = followModel.about,
            grade = followModel.level,
            analysisLanguage = followModel.language,
            registerCountry = null, // FollowModel没有，填null
            isFakeBroadcaster = null, // FollowModel没有，填null
            isShowLowPrice = null, // FollowModel没有，填null
            isSignBroadcaster = followModel.isSignBroadcaster,
            showRoomVersion = followModel.showRoomVersion,
            broadcasterType = followModel.userType,
            isAnswer = null, // FollowModel没有，填null
            isLandScreen = null, // FollowModel没有，填null
            prioritizeDisplayVideo = null, // FollowModel没有，填null
            avatarThumbUrl = followModel.avatarThumbUrl,
            isVip = followModel.isVip ?: false,
            countdown = null // FollowModel没有，填null
        )
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivAvatar: CircleImageView = itemView.findViewById(R.id.iv_avatar)
        val tvUsername: TextView = itemView.findViewById(R.id.tv_username)
        val tvRegion: TextView = itemView.findViewById(R.id.tv_region)
        val ivVideoCall: ImageView = itemView.findViewById(R.id.iv_video_indicator)
    }

    inner class BottomViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvBottomText: TextView = itemView.findViewById(R.id.tv_bottom_text)
        fun bind(text: String) {
            tvBottomText.text = text
        }
    }

} 