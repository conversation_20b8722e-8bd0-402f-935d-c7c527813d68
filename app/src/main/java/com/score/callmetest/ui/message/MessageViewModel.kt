package com.score.callmetest.ui.message

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallStatus
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.GetUserListOnlineStatusPostV2Request
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import kotlin.collections.set

class MessageViewModel : BaseViewModel() {

    private val mHandler = Handler(Looper.getMainLooper())
    private val REFRESH_INTERVAL = 10_000L // 10秒
    private val CACHE_VALID_DURATION = 8_000L // 8秒

    private var mLastStatusTime = 0L // 上一次更新状态时间
    /*
     * 缓存状态<userId, [CallStatus] >
     */
    private val _lastStatusMap = MutableLiveData<Map<String, String>>()
    val lastStatusMap: LiveData<Map<String, String>> = _lastStatusMap


    // <editor-folder desc="更新在线状态">

    /**
     * 查询在线状态
     */
    fun checkOnlineStatus(userIds: List<String>){
        if(userIds.isEmpty()) return
        val currentTime = System.currentTimeMillis()
        val toRefresh = currentTime - mLastStatusTime >= CACHE_VALID_DURATION
        if (!toRefresh) {
            // CACHE_VALID_DURATION时间内不更新状态
            return
        }
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO) {
                    val request = GetUserListOnlineStatusPostV2Request(userIds.toList())
                    RetrofitUtils.dataRepository.getUserListOnlineStatusPostV2(request)
                }

                if (response is NetworkResult.Success) {
                    // 更新状态
                    val statusMap = response.data?:emptyMap()
                    if(statusMap.isEmpty()) return@launch
                    // 返回不为空
                    val currentMap = (_lastStatusMap.value ?: emptyMap()).toMutableMap()
                    statusMap.forEach { (key, statusStr) ->

                        var newStatu = statusStr

                        // 适配审核模式
                        if (StrategyManager.isReviewPkg()) {
                            getUserInfo(key) { userInfo ->
                                if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                                    newStatu = CallStatus.ONLINE
                                }else {
                                    newStatu = GlobalManager.getReviewOtherStatus(userInfo?.userId)
                                }
                            }
                        }

                        // 更新缓存
                        currentMap.put(key, CallStatus.valueOf(newStatu))
                    }
                    _lastStatusMap.postValue(currentMap)
                    mLastStatusTime = System.currentTimeMillis()
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    /**
     * 10秒定时刷新（Fragment可在onResume注册，onPause注销）
     */
    private var isTimerActive = false
    private var onPeriodicRefresh: (() -> Unit)? = null
    private val periodicRefreshRunnable = object : Runnable {
        override fun run() {
            if (isTimerActive) {
                onPeriodicRefresh?.invoke()
                mHandler.postDelayed(this, REFRESH_INTERVAL)
            }
        }
    }
    fun startStatusUpdates(refreshCallback: () -> Unit) {
        onPeriodicRefresh = refreshCallback
        isTimerActive = true
        mHandler.removeCallbacks(periodicRefreshRunnable)
        // 首次立即刷新
        refreshCallback()
        // 10秒后开始定时刷新
        mHandler.postDelayed(periodicRefreshRunnable, REFRESH_INTERVAL)
    }

    fun stopStatusUpdates() {
        isTimerActive = false
        mHandler.removeCallbacks(periodicRefreshRunnable)
        onPeriodicRefresh = null
    }

    // </editor-folder>

} 