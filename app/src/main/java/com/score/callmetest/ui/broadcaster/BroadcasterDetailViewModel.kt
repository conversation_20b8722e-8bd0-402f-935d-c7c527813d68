package com.score.callmetest.ui.broadcaster

import com.score.callmetest.CallStatus
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterExtraInfo
import com.score.callmetest.network.ComplainInsertRecordRequest
import com.score.callmetest.network.GetBroadcasterExtraInfoRequest
import com.score.callmetest.network.GetGiftCountRequest
import com.score.callmetest.network.GetGiftCountResponse
import com.score.callmetest.network.GetUserOnlineStatusPostV2Request
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RemoveBlockRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

typealias OnResult<T> = (result: T?, error: Throwable?) -> Unit

class BroadcasterDetailViewModel : BaseViewModel(), AutoCloseable {
    private val job = Job()
    private val scope = CoroutineScope(Dispatchers.Main + job)

    var userInfo: UserInfo? = null
    var extraUserInfo: BroadcasterExtraInfo? = null
    var giftCount: GetGiftCountResponse? = null
    var userId: String? = null
    var status: String? = null

    fun loadAll(
        userId: String,
        onOnlineStatus: (String?, Throwable?) -> Unit,
        onUserInfo: (UserInfo?, Throwable?) -> Unit,
        onExtraInfo: (BroadcasterExtraInfo?, Throwable?) -> Unit,
        onGiftCount: (GetGiftCountResponse?, Throwable?) -> Unit
    ) {
        this.userId = userId
        UserInfoManager.loadOnlineStatus(scope, userId, onOnlineStatus)
        loadUserInfo(userId, onUserInfo)
        loadExtraInfo(userId, onExtraInfo)
        loadGiftCount(userId, onGiftCount)
    }

    fun loadUserInfo(userId: String, callback: OnResult<UserInfo>) {
        // 先查缓存，再网络查询
        UserInfoManager.getUserInfo(
            userId = userId,
            forceUpdate = true
        ) { getUserInfo ->
            userInfo = getUserInfo
            callback(userInfo, null)
        }
    }

    fun loadExtraInfo(userId: String, callback: (BroadcasterExtraInfo?, Throwable?) -> Unit) {
        // 先查缓存
        val cached = UserInfoManager.getCachedExtraInfo(userId)
        if (cached != null) {
            extraUserInfo = cached
            callback(extraUserInfo, null)
        }
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getBroadcasterExtraInfoPostV2(
                        GetBroadcasterExtraInfoRequest(userId)
                    )
                }
                if(resp is NetworkResult.Success){
                    extraUserInfo = resp.data
                    UserInfoManager.putCachedExtraInfo(userId, extraUserInfo)
                    callback(extraUserInfo, null)
                }

            } catch (e: Exception) {
                callback(null, e)
            }
        }
    }

    fun loadGiftCount(userId: String, callback: (GetGiftCountResponse?, Throwable?) -> Unit) {
        // 先查缓存
        val cached = UserInfoManager.getCachedGiftCount(userId)
        if (cached != null) {
            giftCount = cached as GetGiftCountResponse?
            callback(giftCount, null)
        }
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getGiftCount(GetGiftCountRequest(userId = userId))
                }
                if(resp is NetworkResult.Success){
                    giftCount = resp.data
                    UserInfoManager.putCachedGiftCount(userId, giftCount)
                    callback(giftCount, null)
                }

            } catch (e: Exception) {
                callback(null, e)
            }
        }
    }

    // 取关 - 通过FollowManager
    fun unfollow(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.unfollowUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                userInfo?.isFriend = false
                callback(true)
            }

            override fun onError(errorMsg: String) {
                callback(false)
            }
        })
    }

    // 拉黑
    fun block(userId: String, callback: (Boolean) -> Unit) {
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.insertComplainRecord(
                        ComplainInsertRecordRequest(
                            broadcasterId = userId,
                            channelName = null,
                            complainCategory = "Block",
                            complainSub = null,
                            isAudit = false,
                            reason = null,
                            snapshotPath = null
                        )
                    )
                }
                if (resp is NetworkResult.Success) {
                    userInfo?.isBlock = true
                    callback(true)
                } else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // 取消拉黑
    fun unblock(userId: String, callback: (Boolean) -> Unit) {
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.removeBlock(
                        RemoveBlockRequest(blockUserId = userId)
                    )
                }
                if(resp is NetworkResult.Success){
                    userInfo?.isBlock = false
                    callback(true)
                }else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // 举报（带 complainSub）
    fun reportUser(
        userId: String,
        category: String,
        complainSub: String?,
        callback: (Boolean) -> Unit
    ) {
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.insertComplainRecord(
                        ComplainInsertRecordRequest(
                            broadcasterId = userId,
                            channelName = null,
                            complainCategory = category,
                            complainSub = complainSub,
                            isAudit = false,
                            reason = null,
                            snapshotPath = null
                        )
                    )
                }
                if(resp is NetworkResult.Success){
                    callback(true)
                }else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }


    override fun close() {
        job.cancel()
    }
} 