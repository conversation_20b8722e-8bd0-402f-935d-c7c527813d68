package com.score.callmetest.ui.chat.adapter

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageRightBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.entity.MessageType
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.GlideUtils

/**
 * 发送消息ViewHolder
 */
internal class SentMessageViewHolder(
    private val binding: ItemChatMessageRightBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) :
    RecyclerView.ViewHolder(binding.root),MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null

    // 事件
    init {
        // 消息点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvMessage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageClickListener?.invoke(message, binding.root)
            }
        }

        // image点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivImage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnImageClickListener?.invoke(message, binding.root)
            }
        }

        // 消息长按
        binding.tvMessage.setOnLongClickListener{
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageLongClickListener?.invoke(message, binding.root)
                    ?: false
            } ?: false
        }

        // image长按
        binding.ivImage.setOnLongClickListener{
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnImageLongClickListener?.invoke(message, binding.root)
                    ?: false
            } ?: false
        }

        // 头像
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnAvatarClickListener?.invoke(message)
            }
        }

        // 重发按钮点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivResend){
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnResendClickListener?.invoke(message)
            }
        }
    }

    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message
        // 显示消息内容
        binding.tvMessage.text = message.content
        // 暂时不需要显示时间
//        binding.tvTime.text = message.getFormattedTime()

        updateStatus(message.status)

        // 加载头像
        GlideUtils.load(
            view =binding.ivAvatar,
            url = message.senderAvatar,
            placeholder = R.drawable.placeholder,
            error = R.drawable.placeholder,
            isCircle = true
        )

        // 根据消息类型显示不同内容
        when (message.messageType) {
            MessageType.TEXT -> {
                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                // 其他消息类型视图处理...
            }
            MessageType.IMAGE -> {
                binding.tvMessage.visibility = View.GONE
                binding.ivImage.visibility = View.VISIBLE

                // 加载图片
                GlideUtils.load(
                    view =binding.ivImage,
                    url = message.thumbUri?:message.mediaLocalUri,
                    placeholder = R.drawable.image_placeholder,
                    error = R.drawable.image_error,
                )

            }
            else -> {
                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                // 其他消息类型暂不处理，可根据需求扩展
            }
        }

    }

    override fun updateStatus(status: MessageStatus) {
        // 根据消息状态显示状态图标
        when (status) {
            MessageStatus.SENDING -> {
                binding.progressSending.visibility = View.VISIBLE
                binding.ivStatus.visibility = View.GONE
                binding.ivResend.visibility = View.GONE
            }
            MessageStatus.SENT -> {
                binding.progressSending.visibility = View.GONE
//                binding.ivStatus.setImageResource(R.drawable.ic_sent)
//                binding.ivStatus.visibility = View.VISIBLE
                binding.ivStatus.visibility = View.GONE
                binding.ivResend.visibility = View.GONE
            }
            // 需求暂时没有这些
            /*MessageStatus.RECEIVED -> {
                binding.progressSending.visibility = View.GONE
                binding.ivStatus.setImageResource(R.drawable.ic_received)
                binding.ivStatus.visibility = View.VISIBLE
                binding.ivResend.visibility = View.GONE
            }
            MessageStatus.READ -> {
                binding.progressSending.visibility = View.GONE
                binding.ivStatus.setImageResource(R.drawable.ic_read)
                binding.ivStatus.visibility = View.VISIBLE
                binding.ivResend.visibility = View.GONE
            }*/
            MessageStatus.FAILED -> {
                binding.progressSending.visibility = View.GONE
                binding.ivStatus.visibility = View.GONE
                binding.ivResend.visibility = View.VISIBLE
            }
            else -> {

            }
        }
    }
}