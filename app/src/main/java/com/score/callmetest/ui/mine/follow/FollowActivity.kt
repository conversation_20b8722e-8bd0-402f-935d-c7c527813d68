package com.score.callmetest.ui.mine.follow

import android.os.Bundle
import com.score.callmetest.databinding.ActivityFollowBinding
import com.score.callmetest.ui.base.BaseActivity

/**
 * 关注/粉丝页面的独立Activity
 * 承载FollowFragment，避免与MainActivity的Fragment容器冲突
 */
class FollowActivity : BaseActivity<ActivityFollowBinding, FollowViewModel>() {

    override fun getViewBinding(): ActivityFollowBinding {
        return ActivityFollowBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = FollowViewModel::class.java

    override fun initView() {
        // 获取传入的初始标签页位置
        val initialTab = intent.getIntExtra("initial_tab", 0)
        
        // 创建FollowFragment并设置参数
        val followFragment = FollowFragment().apply {
            arguments = Bundle().apply {
                putInt("initial_tab", initialTab)
            }
        }
        
        // 将FollowFragment添加到Activity中
        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainer.id, followFragment)
            .commit()
    }

    override fun initListener() {
        // 可以在这里添加Activity级别的监听器
        // 比如处理返回按钮等
    }
}
