package com.score.callmetest.ui.mine

import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentMineBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoEvent
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.main.CoinStoreActivity
import com.score.callmetest.ui.mine.follow.FollowActivity
import com.score.callmetest.ui.preview.MultiImagePreviewActivity
import com.score.callmetest.ui.profile.ProfileActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.click

class MineFragment : BaseFragment<FragmentMineBinding, MineViewModel>() {

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMineBinding {
        return FragmentMineBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = MineViewModel::class.java

    override fun initView() {
        // 初始化用户信息
        refreshInfo()

        binding.coinCard.background = DrawableUtils.createRoundRectDrawable(
            ContextCompat.getColor(requireContext(), R.color.bg_coin_card_normal),
            DisplayUtils.dp2pxInternalFloat(14f)
        )
        binding.lineFunction.background = DrawableUtils.createRoundRectDrawable(
            Color.WHITE,
            DisplayUtils.dp2pxInternalFloat(14f)
        )

        if (StrategyManager.isReviewPkg()) {
            binding.customerService.visibility = View.GONE
            binding.coinCard.visibility = View.GONE
        }
    }

    override fun initData() {
        super.initData()
        // 初始化显示为0
        binding.tvFollowing.text = "0"
        binding.tvFollowers.text = "0"

        // 获取关系数量统计
        viewModel.getRelationsCounter()

        // 监听用户信息变化事件
        setupObservers()
    }

    private fun setupObservers() {
        // 监听关系数量统计数据
        viewModel.relationsCounter.observe(viewLifecycleOwner) { relationsCounter ->
            binding.tvFollowing.text = relationsCounter.followingCounter.toString()
            binding.tvFollowers.text = relationsCounter.followerCounter.toString()
        }


        // 监听用户信息变化事件
        EventBus.observe(this, UserInfoEvent::class.java) { event ->
            event.userInfo?.let { userInfo ->
                refreshInfo()
            }
        }

        // 监听金币余额
        DualChannelEventManager.observeAvailableCoins(viewLifecycleOwner) { availableCoinsMessage ->
            // 更新余额
            val availableCoins = availableCoinsMessage.coins ?: 0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
            binding.tvCoinValue.text = availableCoins.toString()
        }

        // 通过这个MainActivity的observer，监听底部tab切换，当切换到MineFragment时，更新关系数量
        EventBus.observe(this, CustomEvents.BottomTabSelected::class.java){ tab ->
            if (tab.index == 2) {
                // 获取最新的关系数量统计
                viewModel.getRelationsCounter()
            }
        }

        // 监听关注或者取消关注事件发生
        EventBus.observe(this, FollowManager.FollowListRefreshEvent::class.java){event ->
            // 获取最新的关系数量统计
            viewModel.getRelationsCounter()
        }
    }

    override fun initListener() {
        // 这里只做示例，实际应遍历include的item_mine_function并设置icon/title/subtitle
        binding.language.click {
            val sheet = LanguageSelectBottomSheet { langCode ->
                LanguageUtils.setAppLanguage(requireContext().applicationContext, langCode)
                // 可选：重启Activity以立即生效
                activity?.recreate()
            }
            sheet.show(parentFragmentManager, "LanguageSelectBottomSheet")
        }

        binding.btnEdit.click {
            ActivityUtils.startActivity(
                <EMAIL>(),
                ProfileActivity::class.java
            )
        }

        // 关注列表点击事件
        binding.followingCard.click {
            val bundle = android.os.Bundle().apply {
                putInt("initial_tab", 0) // 0表示关注页面
            }
            ActivityUtils.startActivity(
                requireContext(),
                FollowActivity::class.java,
                bundle
            )
        }

        // 粉丝列表点击事件
        binding.followerCard.click {
            val bundle = android.os.Bundle().apply {
                putInt("initial_tab", 1) // 1表示粉丝页面
            }
            ActivityUtils.startActivity(
                requireContext(),
                FollowActivity::class.java,
                bundle
            )
        }

        //设置入口
        binding.settings.click {
            ActivityUtils.startActivity(requireContext(), SettingsActivity::class.java)
        }

        binding.customerService.click {
            // 先查缓存，再网络查询
            UserInfoManager.getUserInfo(StrategyManager.strategyConfig?.userServiceAccountId){ getUserInfo ->
                getUserInfo?.let { nonNullUser ->
                    ChatActivity.start(context, nonNullUser)
                }
            }
        }

        // 头像点击预览
        binding.ivAvatar.click {
            val avatarUrl = UserInfoManager.myUserInfo?.avatarUrl
            if (!avatarUrl.isNullOrEmpty()) {
                val intent = Intent(requireContext(), MultiImagePreviewActivity::class.java)
                val uri = avatarUrl.toUri()
                val uriList = arrayListOf(uri)
                intent.putParcelableArrayListExtra("imageUris", uriList)
                intent.putExtra("startIndex", 0)
                startActivity(intent)
            }
        }

        binding.coinCard.click {
            ActivityUtils.startActivity(requireContext(), CoinStoreActivity::class.java)
        }

    }

    fun refreshInfo() {
        if (UserInfoManager.myUserInfo == null) {
            return
        }

        GlideUtils.load(
            context = <EMAIL>(),
            imageView = binding.ivAvatar,
            placeholder = R.drawable.placeholder,
            url = UserInfoManager.myUserInfo?.avatarUrl
        )

        // 顶部信息
        binding.tvNickname.text = UserInfoManager.myUserInfo!!.nickname
        binding.tvNickname.setTypeface(Typeface.create("sans-serif", Typeface.BOLD))

        // 初始化金币显示
        binding.tvCoinValue.text = (UserInfoManager.myUserInfo?.availableCoins ?: 0).toString()

        binding.language.setTip(LanguageUtils.getSystemLanguage())
    }

}