package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.opensource.svgaplayer.SVGACallback
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import org.chromium.base.ThreadUtils.runOnUiThread

class FollowButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AlphaFrameLayout(context, attrs, defStyleAttr) {

    private val followBgSvga: SVGAImageView
    private val followIcon: ImageView
    private val followActionSvga: SVGAImageView
    private val followText: TextView
    private var isFriend: Boolean = false
    private var followHighlightTaskActive = false

    init {
        LayoutInflater.from(context).inflate(R.layout.view_follow_button, this, true)
        followBgSvga = findViewById(R.id.follow_bg_svga)
        followIcon = findViewById(R.id.follow_icon)
        followActionSvga = findViewById(R.id.follow_action_svga)
        followText = findViewById(R.id.follow_text)


        followText.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
    }

    fun setIsFriend(friend: Boolean) {
        isFriend = friend
        if (friend) {
            // 已关注，隐藏按钮，停止动画
            visibility = View.GONE
            followHighlightTaskActive = false
            followActionSvga.stopAnimation()
            stopFollowHighlightTask()
        } else {
            // 未关注，显示按钮，重置状态
            visibility = View.VISIBLE
            isEnabled = true
            followIcon.visibility = View.VISIBLE
            followText.visibility = View.VISIBLE
            followActionSvga.stopAnimation()
            followActionSvga.visibility = View.GONE
            followHighlightTaskActive = true
            startFollowHighlightTask()
        }
    }

    fun playFollowAnim(onEnd: (() -> Unit)? = null) {
        isEnabled = false
        followIcon.visibility = View.GONE
        followActionSvga.visibility = View.VISIBLE
        val parser = SVGAParser(context)
        parser.decodeFromAssets("do_follow.svga", object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                followActionSvga.setVideoItem(videoItem)
                followActionSvga.loops = 1
                followActionSvga.startAnimation()
                followActionSvga.callback = object : SVGACallback {
                    override fun onFinished() {
                        onEnd?.invoke()
                    }

                    override fun onPause() {
                    }

                    override fun onRepeat() {
                    }

                    override fun onStep(frame: Int, percentage: Double) {

                    }
                }
            }
            override fun onError() {
                isEnabled = true
                onEnd?.invoke()
            }
        })
    }

    fun startFollowHighlightTask() {
        if (!followHighlightTaskActive) return
        val parser = SVGAParser(context)
        parser.decodeFromAssets("follow_highlight.svga", object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                followBgSvga.setVideoItem(videoItem)
                followBgSvga.loops = 1
                followBgSvga.startAnimation()
            }
            override fun onError() {}
        })
        ThreadUtils.runOnMainDelayed(10000) {
            startFollowHighlightTask()
        }
    }

    fun stopFollowHighlightTask() {
        followHighlightTaskActive = false
        followBgSvga.stopAnimation()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        followHighlightTaskActive = false
        followActionSvga.stopAnimation()
        followBgSvga.stopAnimation()
    }

    fun addFriend(scope: CoroutineScope, userId: String, callback: (Boolean) -> Unit) {
        isEnabled = false

        FollowManager.followUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                runOnUiThread {
                    EventBus.post(FollowEvent(userId, true))
                    playFollowAnim {
                        setIsFriend(true)
                    }
                    callback.invoke(true)
                }
            }

            override fun onError(errorMsg: String) {
                runOnUiThread {
                    EventBus.post(FollowEvent(userId, false))
                    setIsFriend(false)
                    callback.invoke(false)
                }
            }
        })
    }
}

data class FollowEvent(
    val userId: String,
    val isFriend: Boolean
)