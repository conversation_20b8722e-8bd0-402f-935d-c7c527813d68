package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import androidx.core.graphics.toColorInt
import com.score.callmetest.util.click
import com.score.callmetest.util.StatusBarUtils

class CountrySelectBottomSheet(
    private val context: Context,
    private val selectedCountry: String?,
    private val onCountrySelected: (String) -> Unit
) : BottomSheetDialogFragment() {

    private lateinit var rvCountry: RecyclerView
    private lateinit var cancelBtn: ImageView
    // 国家列表
    private val countryList = CountryUtils.getAllCountries()

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        // 禁用默认背景
        dialog.setOnShowListener {
            val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.bottom_sheet_country_select, container, false)
        // 设置底部弹窗内容区域圆角背景（内容区域）
        DrawableUtils.createRoundRectDrawable(
            Color.WHITE,
            floatArrayOf(
                DisplayUtils.dp2pxInternalFloat(30f), DisplayUtils.dp2pxInternalFloat(30f), // 左上
                DisplayUtils.dp2pxInternalFloat(30f), DisplayUtils.dp2pxInternalFloat(30f), // 右上
                0f, 0f, // 右下
                0f, 0f  // 左下
            )
        ).also { view.background = it }
        rvCountry = view.findViewById(R.id.rv_country)
        cancelBtn = view.findViewById(R.id.image_cancel)
        rvCountry.layoutManager = LinearLayoutManager(context)
        // 修正：为 RecyclerView 设置 paddingBottom 并关闭 clipToPadding，防止最后一项被遮挡
        rvCountry.setPadding(
            rvCountry.paddingLeft,
            rvCountry.paddingTop,
            rvCountry.paddingRight,
            DisplayUtils.dp2pxInternal(30f)
        )
        rvCountry.clipToPadding = false
        val adapter = CountryAdapter(countryList, selectedCountry) { countryName ->
            onCountrySelected(countryName)
            dismiss()
        }
        rvCountry.adapter = adapter
        cancelBtn.click { dismiss() }

        // 设置RecyclerView底部padding，避免被导航栏遮挡
        val navigationBarHeight = StatusBarUtils.getNavigationBarHeight(requireContext())
        rvCountry.setPadding(
            rvCountry.paddingLeft,
            rvCountry.paddingTop,
            rvCountry.paddingRight,
            rvCountry.paddingBottom + navigationBarHeight
        )
        rvCountry.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: android.graphics.Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val margin = DisplayUtils.dp2pxInternal(5f)
                val lastMargin = DisplayUtils.dp2pxInternal(20f)
                val position = parent.getChildAdapterPosition(view)
                val isLast = position == (parent.adapter?.itemCount?.minus(1) ?: -1)
                outRect.top = margin
                outRect.bottom = if (isLast) lastMargin else margin
            }
        })
        return view
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog as? com.google.android.material.bottomsheet.BottomSheetDialog
        val bottomSheet = dialog?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        //禁止弹窗滑动
        bottomSheet?.let {
            val behavior = com.google.android.material.bottomsheet.BottomSheetBehavior.from(it)
            behavior.isDraggable = false // 禁止拖拽
            behavior.skipCollapsed = true // 跳过折叠状态
            behavior.isHideable = false  // 禁止下拉关闭
        }
    }

    class CountryAdapter(
        private val countries: List<CountryUtils.Country>,
        private val selectedCountry: String?,
        private val onItemClick: (String) -> Unit
    ) : RecyclerView.Adapter<CountryAdapter.CountryViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CountryViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_bottom_sheet_country_select, parent, false)
            return CountryViewHolder(view)
        }

        override fun onBindViewHolder(holder: CountryViewHolder, position: Int) {
            val country = countries[position]
            holder.tvCountryName.text = country.enName
       /*     // 设置国家图标，如果没有图标则使用默认图标
            val iconRes = country.iconRes ?: R.drawable.map_language
            holder.ivCountryIcon.setImageResource(iconRes)*/
            val isSelected = country.enName == selectedCountry
            holder.itemView.isSelected = isSelected
            holder.itemView.click { onItemClick(country.enName) }
            // 根据选中状态设置不同背景
            if (isSelected) {
                holder.itemView.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                    fillColor = "#ffe5fffd".toColorInt(),
                    radiusDp = 30f,
                    strokeColor = "#ff55ecdf".toColorInt(),
                    strokeWidth = DisplayUtils.dp2pxInternal(1f)
                )
            } else {
                holder.itemView.background = DrawableUtils.createRoundRectDrawable(
                   color = "#fff3f5fa".toColorInt(),
                   radius = DisplayUtils.dp2pxInternalFloat(30f)
                )
            }
        }

        override fun getItemCount(): Int = countries.size

        class CountryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            //val ivCountryIcon: ImageView = itemView.findViewById(R.id.iv_country_icon)
            val tvCountryName: TextView = itemView.findViewById(R.id.tv_flag_country_name)
        }
    }
} 