package com.score.callmetest.ui.widget

import android.app.Dialog
import android.os.Bundle
import android.graphics.Color
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.util.click

/**
 * 通用底部操作对话框，可用于拉黑、举报、关注等功能
 */
class CommonActionBottomSheet private constructor(
    private val title: String? = null,
    private val actions: List<ActionItem>
) : BottomSheetDialogFragment() {

    data class ActionItem(
        val text: String,
        val onClick: () -> Unit
    )

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.bottom_sheet_common_actions, container, false)
        
        // 设置标题（如果有）
        val tvTitle = view.findViewById<TextView>(R.id.tv_title)
        if (!title.isNullOrEmpty()) {
            tvTitle.text = title
            tvTitle.visibility = View.VISIBLE
        } else {
            tvTitle.visibility = View.GONE
        }
        
        // 添加操作项
        val actionContainer = view.findViewById<LinearLayout>(R.id.action_container)
        actions.forEach { action ->
            val actionView = inflater.inflate(R.layout.item_action_option, actionContainer, false) as TextView
            actionView.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
            actionView.text = action.text
            actionView.click {
                action.onClick()
                dismiss()
            }
            actionContainer.addView(actionView)
        }
        
        // 设置取消按钮
        val cancel = view.findViewById<TextView>(R.id.btn_cancel)
        cancel.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
        cancel.click { dismiss() }
        
        return view
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener {
            val bottomSheet = (dialog as? BottomSheetDialog)
                ?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundColor(Color.TRANSPARENT)
        }
        return dialog
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
    }

    /**
     * 构建器模式，用于更灵活地创建底部对话框
     */
    class Builder {
        private var title: String? = null
        private val actions = mutableListOf<ActionItem>()

        fun setTitle(title: String): Builder {
            this.title = title
            return this
        }

        fun addAction(text: String, onClick: () -> Unit): Builder {
            actions.add(ActionItem(text, onClick))
            return this
        }

        fun build(): CommonActionBottomSheet {
            return CommonActionBottomSheet(title, actions)
        }
    }

    companion object {
        fun builder(): Builder = Builder()
    }
} 