package com.score.callmetest.ui.mine.blockList

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.network.BlockListItem
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.launch
import timber.log.Timber

class BlockListViewModel : ViewModel() {
    private val _blockList = MutableLiveData<List<BlockListItem>>()
    val blockList: LiveData<List<BlockListItem>> = _blockList

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 分页状态管理
    private var currentPage = 1
    private var isLoadingMore = false
    private var hasMoreData = true
    private val pageSize = 15

    // 当前列表数据
    private val currentBlockList = mutableListOf<BlockListItem>()

    /**
     * 获取屏蔽列表
     * @param isRefresh 是否是刷新操作，true表示刷新，false表示加载更多
     */
    fun fetchBlockList(isRefresh: Boolean = true) {
        if (isRefresh) {
            // 刷新操作，重置分页状态
            currentPage = 1
            hasMoreData = true
            currentBlockList.clear()
        } else {
            // 加载更多操作，检查是否正在加载或没有更多数据
            if (isLoadingMore || !hasMoreData) {
                return
            }
            currentPage++
        }

        isLoadingMore = true
        _isLoading.postValue(true)
        viewModelScope.launch {
            try {
                val request = com.score.callmetest.network.UserBlockListRequest(limit = pageSize, page = currentPage)
                val response = RetrofitUtils.dataRepository.getBlockList(request)
                if(response is NetworkResult.Success){
                    val list = response.data ?: emptyList()

                    if (isRefresh) {
                        currentBlockList.clear()
                    }
                    currentBlockList.addAll(list)

                    // 判断是否还有更多数据
                    hasMoreData = list.size >= pageSize

                    _blockList.postValue(currentBlockList.toList())
                    Timber.d("fetchBlockList: ${_blockList.value.toString()}")
                }else {
                    if (isRefresh) {
                        currentBlockList.clear()
                        _blockList.postValue(emptyList())
                    }
                    _error.postValue(CallmeApplication.context.getString(R.string.fetch_block_list_failed))
                    Timber.d(" fetchBlockList: fail")
                }
            } catch (e: Exception) {
                if (!isRefresh) {
                    // 加载更多失败时，回退页码
                    currentPage--
                }
                e.toString().logAsTag(this.javaClass.name + " fetchBlockList: exception")
                _error.postValue(e.message ?: CallmeApplication.context.getString(R.string.network_error))
            } finally {
                isLoadingMore = false
                _isLoading.postValue(false)
            }
        }
    }

    // 取消拉黑
    fun unblock(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
                    RetrofitUtils.dataRepository.removeBlock(
                        com.score.callmetest.network.RemoveBlockRequest(blockUserId = userId)
                    )
                }
                if (resp is NetworkResult.Success) {
                    // userInfo?.isBlock = false // 这里没有userInfo对象，视具体业务可补充
                    callback(true)
                } else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    /**
     * 检查是否还有更多数据
     */
    fun hasMoreData(): Boolean = hasMoreData

    /**
     * 检查是否正在加载更多
     */
    fun isLoadingMore(): Boolean = isLoadingMore
}