package com.score.callmetest.ui.home

import android.graphics.Typeface
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentHomeBinding
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.LastSpecialOfferResponse
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.ui.widget.PromotionDialogHelper
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.click
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.util.TimeUtils
import kotlinx.coroutines.Job
import timber.log.Timber

class HomeFragment : BaseFragment<FragmentHomeBinding, HomeViewModel>() {

    private var currentTab1Position = 0
    private var countDownJob: Job? = null // 倒计时协程Job
    private var  allowScrollControl = false; // 是否允许滚动控制fab1Layout标志

    // 记录每个一级Tab下的二级Tab索引
    private var tab2SelectedPositions = mutableMapOf<Int, Int>()
    private lateinit var tab1Layout: TabLayout
    private lateinit var tab2Layout: TabLayout
    private lateinit var viewPager: ViewPager2
    private var countryFilterPopup: CountryFilterPopupWindow? = null

    // 添加弹窗状态管理
    private var isDialogShowing = false

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentHomeBinding {
        return FragmentHomeBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = HomeViewModel::class.java

    override fun initView() {
        tab1Layout = binding.tabLayout1
        tab2Layout = binding.tabLayout2
        viewPager = binding.viewPager

        // 设置TabLayout的稳定性配置
        tab1Layout.tabRippleColor = null
        tab2Layout.tabRippleColor = null

        setupTabs()
        setupViewPager()
        setupCountryFilter()
    }

    override fun initListener() {
        // 监听主播墙滑动状态
        if (!StrategyManager.isReviewPkg()) {
            EventBus.observe(this, HomeViewModel.WallScrollEvent::class.java) { event ->
                when (event.state) {
                    RecyclerView.SCROLL_STATE_IDLE -> {
                        if (allowScrollControl){
                            binding.fab1Layout.visibility = View.VISIBLE
                        }
                        binding.fab2.visibility = View.VISIBLE
                    }
                    else -> {
                        binding.fab1Layout.visibility = View.GONE
                        binding.fab2.visibility = View.GONE
                    }
                }
            }
        } else {
            binding.fab1Layout.visibility = View.GONE
            binding.fab2.visibility = View.GONE
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 悬浮按钮动效和背景
        setupFloatingSvgaButtons()
        // 初始化时加载数据（优先加载新人促销）
        viewModel.fetchPresentedCoins()
    }


    private fun setupTabs() {
        // 设置一级Tab的选择监听
        tab1Layout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, true)
                currentTab1Position = tab.position
                updateTab2(currentTab1Position)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        })

        // 设置二级Tab的选择监听
        tab2Layout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, true)

                if (!updatingTab2) {
                    viewPager.currentItem = getGlobalPosition(currentTab1Position, tab.position)
                }

                // 当tab1为第一个标签时，更新国家筛选UI
                if (currentTab1Position == 0) {
                    val tab2Name = viewModel.broadcasterWallTabDataLists?.get(currentTab1Position)?.subTagList?.get(tab.position)
                    // 查找与tab2文本匹配的国家代码
                    val countryCode = tab2Name?.let { CountryUtils.getCountryByEnName(it)?.iso ?:"ALL" }
                    updateCountryFilterUI(countryCode)
                    // 更新ViewModel中的国家代码
                   // viewModel.setSelectedCountry(countryCode)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabAppearance(tab, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        })

        // 初始化一级Tab
        clearTabLayout(tab1Layout)

        viewModel.broadcasterWallTabDataLists?.forEachIndexed { index, tab1Data ->
            val tab = tab1Layout.newTab()
            tab.customView = createCustomTabView(tab1Data.tagName ?: "", index == 0)
            tab1Layout.addTab(tab)
        }
        // 初始化tab2记录表
        for (i in 0..tab1Layout.tabCount - 1) {
            tab2SelectedPositions.put(i, 0)
        }
        // 初始化二级Tab
        clearTabLayout(tab2Layout)
        updateTab2(0)
    }

    private fun createCustomTabView(text: String, selected: Boolean): View {
        val view = layoutInflater.inflate(R.layout.tab_custom, null)
        val textView = view.findViewById<TextView>(R.id.tab_text)
        val indicator = view.findViewById<View>(R.id.tab_indicator)
        textView.text = text
        textView.textSize = if (selected) 22f else 15f
        textView.paint.isFakeBoldText = selected
        textView.setTypeface(
            if (selected) Typeface.create("sans-serif", Typeface.BOLD)
            else Typeface.create("sans-serif", Typeface.NORMAL)
        )

        // 先重置indicator
        indicator.visibility = if (selected) View.VISIBLE else View.GONE

        // 使用ViewTreeObserver避免post操作的不稳定性
        if (selected) {
            textView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    textView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (textView.width > 0) {
                        indicator.layoutParams.width = textView.width / 2
                        indicator.layoutParams.height = textView.height / 2
                        indicator.requestLayout()
                    }
                }
            })
        }
        return view
    }

    private fun setupViewPager() {
        viewPager.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = getTotalItemCount()

            private fun newWallFragment(position: Int): Fragment {
                val (tab1Index, tab2Index) = getTab1AndTab2Index(position)
                val tab1Name = viewModel.broadcasterWallTabDataLists!![tab1Index].tagName ?: ""
                val tab2Name =
                    viewModel.broadcasterWallTabDataLists!![tab1Index].subTagList?.get(tab2Index)
                        ?: "All"

                // region参数通过arguments传递
                return WallFragment.newInstance(
                    tab1Name,
                    tab2Name,
                    viewModel.getSelectedCountryCode()
                )
            }

            override fun createFragment(position: Int): Fragment {
                return newWallFragment(position)
            }

            // 为了支持国家筛选变化时的刷新，重写以下方法
            override fun getItemId(position: Int): Long {
                // 结合position和当前国家代码的hash值，确保国家变化时能正确刷新
                val countryCodeHash = viewModel.getSelectedCountryCode()?.hashCode()?.toLong() ?: 0L
                return position.toLong() + (countryCodeHash shl 32)
            }

            override fun containsItem(itemId: Long): Boolean {
                // 简化实现，通常可以返回true
                return true
            }
        }

        viewPager.offscreenPageLimit = 3
        // 设置用户输入类型，减少不必要的滑动冲突
        viewPager.isUserInputEnabled = true

        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                val (tab1Index, tab2Index) = getTab1AndTab2Index(position)
                // 只在这里更新tab2最新记录
                tab2SelectedPositions[tab1Index] = tab2Index

                // 避免在更新Tab2期间触发额外的同步
                if (!updatingTab2) {
                    if (currentTab1Position != tab1Index) {
                        // 使用post确保Tab切换的顺序正确
                        tab1Layout.post {
                            tab1Layout.getTabAt(tab1Index)?.select()
                        }
                    }

                    // 确保二级Tab存在且可见时才选择
                    if (tab2Layout.isVisible && tab2Layout.tabCount > tab2Index) {
                        tab2Layout.post {
                            tab2Layout.getTabAt(tab2Index)?.select()
                        }
                    }
                }
                checkCountryFilter()
            }
        })
    }

    // 添加辅助方法来更新Tab外观
    private fun updateTabAppearance(tab: TabLayout.Tab?, selected: Boolean) {
        val view = tab?.customView
        val textView = view?.findViewById<TextView>(R.id.tab_text)
        val indicator = view?.findViewById<View>(R.id.tab_indicator)

        if (selected) {
            textView?.textSize = 22f
            textView?.paint?.isFakeBoldText = true
            textView?.setTypeface(Typeface.create("sans-serif", Typeface.BOLD))
            indicator?.visibility = View.VISIBLE

            // 使用ViewTreeObserver确保布局稳定
            textView?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    textView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (textView.width > 0 && indicator != null) {
                        indicator.layoutParams.width = textView.width / 2
                        indicator.layoutParams.height = textView.height / 2
                        indicator.requestLayout()
                    }
                }
            })
        } else {
            textView?.textSize = 15f
            textView?.paint?.isFakeBoldText = false
            textView?.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL))
            indicator?.visibility = View.GONE
        }
    }

    var updatingTab2: Boolean = false
    private fun updateTab2(tab1Position: Int) {
        updatingTab2 = true
        val subTagList = viewModel.broadcasterWallTabDataLists?.get(tab1Position)?.subTagList
        // 判断是否只下发了一个“All”
        if (subTagList != null && subTagList.size == 1 && subTagList[0].equals(
                "All",
                ignoreCase = true
            )
        ) {
            // 使用动画隐藏二级Tab，避免突然消失
            if (tab2Layout.isVisible) {
                tab2Layout.animate()
                    .alpha(0f)
                    .setDuration(150)
                    .withEndAction {
                        tab2Layout.visibility = View.GONE
                        tab2Layout.alpha = 1f
                    }
                    .start()
            }
            viewPager.currentItem = getGlobalPosition(currentTab1Position, 0)
        } else {
            // 确保二级Tab可见
            if (tab2Layout.visibility != View.VISIBLE) {
                tab2Layout.alpha = 0f
                tab2Layout.visibility = View.VISIBLE
                tab2Layout.animate()
                    .alpha(1f)
                    .setDuration(150)
                    .start()
            }

            // 只有在Tab内容真正改变时才重建
            val needRebuild = tab2Layout.tabCount != (subTagList?.size ?: 0) ||
                    subTagList?.mapIndexed { index, tabData ->
                        val existingTab = tab2Layout.getTabAt(index)
                        val existingText = existingTab?.customView?.findViewById<TextView>(R.id.tab_text)?.text
                        existingText != tabData
                    }?.any { it == true } ?: true

            if (needRebuild) {
                tab2Layout.removeAllTabs()
                subTagList?.forEach { tab2Data ->
                    val tab = tab2Layout.newTab()
                    tab.customView = createCustomTabView(tab2Data, false)
                    tab2Layout.addTab(tab)
                }

                // 恢复选中状态
                val tab2Index = tab2SelectedPositions.getOrElse(tab1Position) { 0 }
                if (tab2Layout.tabCount > 0) {
                    val targetIndex = tab2Index.coerceAtMost(tab2Layout.tabCount - 1)
                    // 延迟选择，确保Tab已经完全添加
                    tab2Layout.post {
                        if (targetIndex == 0 && tab2Layout.selectedTabPosition != 0) {
                            // 手动触发第一个Tab的选中状态
                            tab2Layout.getTabAt(0)?.select()
                            updateTabAppearance(tab2Layout.getTabAt(0), true)
                        } else if (targetIndex > 0) {
                            tab2Layout.getTabAt(targetIndex)?.select()
                        }
                        viewPager.currentItem = getGlobalPosition(currentTab1Position, targetIndex)
                    }
                }
            } else {
                // 如果不需要重建，只更新选中状态
                val tab2Index = tab2SelectedPositions.getOrElse(tab1Position) { 0 }
                if (tab2Layout.tabCount > 0) {
                    val targetIndex = tab2Index.coerceAtMost(tab2Layout.tabCount - 1)
                    if (tab2Layout.selectedTabPosition != targetIndex) {
                        tab2Layout.getTabAt(targetIndex)?.select()
                    }
                    viewPager.currentItem = getGlobalPosition(currentTab1Position, targetIndex)
                }
            }
        }
        updatingTab2 = false
    }

    /**
     * 把所有的subtab相加
     */
    private fun getTotalItemCount(): Int {
        if (viewModel.broadcasterWallTabDataLists == null) {
            return 0
        }
        return viewModel.broadcasterWallTabDataLists!!.sumOf { it.subTagList?.size ?: 0 }
    }

    /**
     * 获取当前的Fragment属于总数的第几个
     */
    private fun getGlobalPosition(tab1Position: Int, tab2Position: Int): Int {
        if (viewModel.broadcasterWallTabDataLists == null) {
            return 0
        }
        var position = 0
        for (i in 0 until tab1Position) {
            position += viewModel.broadcasterWallTabDataLists!![i].subTagList?.size ?: 0
        }
        return position + tab2Position
    }

    private fun getTab1AndTab2Index(globalPosition: Int): Pair<Int, Int> {
        if (viewModel.broadcasterWallTabDataLists == null) {
            return Pair(0, 0)
        }
        var remainingPosition = globalPosition
        for (i in viewModel.broadcasterWallTabDataLists!!.indices) {
            val subTabsSize = viewModel.broadcasterWallTabDataLists!![i].subTagList?.size ?: 0
            if (remainingPosition < subTabsSize) {
                return Pair(i, remainingPosition)
            }
            remainingPosition -= subTabsSize
        }
        return Pair(0, 0)
    }

    // 彻底清理 TabLayout 的所有子 View，防止重影
    private fun clearTabLayout(tabLayout: TabLayout) {
        tabLayout.removeAllTabs()
        for (i in 0 until tabLayout.childCount) {
            val child = tabLayout.getChildAt(i)
            if (child is ViewGroup) {
                child.removeAllViews()
            }
        }
    }

    private fun checkCountryFilter() {
        binding.countryFilter.visibility =
            if (currentTab1Position == 0
                && !StrategyManager.isReviewPkg()
                && tab2SelectedPositions[currentTab1Position] == 0
            )
                View.VISIBLE
            else
                View.GONE
    }

    private fun setupCountryFilter() {
        if (StrategyManager.isReviewPkg()) {
            binding.countryFilter.visibility = View.GONE
            return
        }

        // 观察国家筛选状态变化
        viewModel.selectedCountryCode.observe(viewLifecycleOwner) { countryCode ->
            updateCountryFilterUI(countryCode)
        }

        // 添加点击事件监听器
        binding.countryFilter.click {
            showCountryFilterDialog()
        }
    }

    private fun showCountryFilterDialog() {
        if (countryFilterPopup == null) {
            countryFilterPopup = CountryFilterPopupWindow(requireContext()) { countryCode ->
                viewModel.setSelectedCountry(countryCode)
                // 只在第一个父标签下处理
                val tab1Index = 0
                val subTagList = viewModel.broadcasterWallTabDataLists?.get(tab1Index)?.subTagList
                // 优先用英文名匹配subTagList
                val countryEnName = countryCode?.let { CountryUtils.getCountryByIso(it)?.enName }
                val tab2Index =
                    subTagList?.indexOfFirst { it.equals(countryEnName, ignoreCase = true) }
                        ?.takeIf { it >= 0 } ?: -1
                if (tab2Index >= 0) {
                    // 切换到对应的Tab2
                    viewPager.currentItem = getGlobalPosition(currentTab1Position, tab2Index)
                    tab2Layout.getTabAt(tab2Index)?.select()
                } else {
                    tab2Layout.getTabAt(tab2Index)?.select()
                    viewPager.adapter?.notifyDataSetChanged()
                }

                // 弹窗消失
                countryFilterPopup?.dismiss()
            }
        }
        countryFilterPopup?.show(binding.tabLayout1)
    }

    private fun updateCountryFilterUI(countryCode: String?) {
        DrawableUtils.setRoundRectBackground(
            binding.tvHomeCountry,
            0xfff7ff38.toInt(),
            DisplayUtils.dp2pxInternal(6f).toFloat()
        )
        if (countryCode == null || countryCode == "ALL") {
            binding.tvHomeCountry.text = "All"
            binding.imageHomeCountry.setImageResource(R.drawable.map_language)
        } else {
            binding.tvHomeCountry.text = countryCode
            val iconRes = CountryUtils.getCountryIconResByIso(countryCode)
            binding.imageHomeCountry.setImageResource(iconRes)
        }
    }

    /**
     * 设置SVGA悬浮按钮动画和背景
     */
    private fun setupFloatingSvgaButtons() {
        if (StrategyManager.isReviewPkg()) {
            binding.fab1Layout.visibility = View.GONE
            return
        }

        // 设置圆角黑色半透明背景
        DrawableUtils.setRoundRectBackground(
            binding.fab1TimerText,
            ContextCompat.getColor(requireContext(), R.color.black_50),
            DisplayUtils.dp2pxInternal(9f).toFloat()
        )
        // 播放SVGA动画
        playSvgaAnim(binding.fab1, "purple_gift.svga")
        playSvgaAnim(binding.fab2, "orange_gift.svga")

        //活动弹窗的逻辑
        setupPromotionLogic()

        //弹窗半屏支付界面
        binding.fab2.click {
            CoinRechargeDialog().show(requireActivity().supportFragmentManager, "coin_recharge")
        }
    }

    private fun setupPromotionLogic() {
        // 合并观察逻辑
        val promotionObserver = Observer<Any?> { _ ->
            when {
                // 1. 优先检查新人促销
                viewModel.presentedCoinsLiveData.value != null -> {
                    val data = viewModel.presentedCoinsLiveData.value!!
                    updateCountdown(data.surplusMillisecond ?: data.remainMilliseconds)
                }
                // 2. 其次检查活动促销
                viewModel.promotionModelLiveData.value != null -> {

                    val data = viewModel.promotionModelLiveData.value!!
                    updateCountdown( data.remainMilliseconds)
                }
                // 3. 都没有数据
                else -> {
                    countDownJob?.cancel()
                    binding.fab1TimerText.text = "00:00:00"
                    binding.fab1Layout.isVisible = false // 隐藏fab1
                }
            }
        }

        // 同时观察两个LiveData，但使用同一个处理逻辑
        viewModel.presentedCoinsLiveData.observe(viewLifecycleOwner, promotionObserver)
        viewModel.promotionModelLiveData.observe(viewLifecycleOwner, promotionObserver)

        // 设置fab1点击事件
        binding.fab1.click {
            if (canShowDialog()) {
                showPromotionDialogByPriority()
            }
        }
    }

    private fun canShowDialog(): Boolean {
        return !(viewModel.presentedCoinsLoading.value == true ||
                viewModel.promotionLoading.value == true ||
                isDialogShowing)
    }

    private fun updateCountdown(milliseconds: Long?) {
        val remainMs = milliseconds ?: 0L
        if (remainMs > 0) {
            binding.fab1Layout.isVisible = true // 有活动时显示fab1
            allowScrollControl = true

            // 根据当前活动类型确定activityType和activityId
            val (activityType, activityId) = when {
                viewModel.presentedCoinsLiveData.value != null -> {
                    val data = viewModel.presentedCoinsLiveData.value!!
                    CountdownManager.ActivityType.PRESENTED_COINS to (data.code ?: "presented_coins")
                }
                viewModel.promotionModelLiveData.value != null -> {
                    val data = viewModel.promotionModelLiveData.value!!
                    CountdownManager.ActivityType.SPECIAL_PROMOTION to (data.code ?: "special_promotion")
                }
                else -> {
                    CountdownManager.ActivityType.SPECIAL_PROMOTION to "unknown_promotion"
                }
            }

            startCountdownTimer(binding.fab1TimerText, remainMs, activityType, activityId)
        } else {
            binding.fab1Layout.isVisible = false // 没有有效活动时隐藏fab1
            allowScrollControl = false
        }
    }

    /**
     * 按优先级显示促销弹窗：新人促销 > 活动促销
     */
    private fun showPromotionDialogByPriority() {
        when {
            viewModel.presentedCoinsLiveData.value != null -> {
                showPresentedCoinsDialog(viewModel.presentedCoinsLiveData.value!!)
            }
            viewModel.promotionModelLiveData.value != null -> {
                showPromotionDialog(viewModel.promotionModelLiveData.value!!)
            }
            else -> {
                // 如果都没有数据，重新获取（优先获取新人促销）
                Timber.tag("HomeFragment").d("No promotion data, fetching presented coins")
            }
        }
    }



    private fun showPromotionDialog(data: LastSpecialOfferResponse) {
        if (isDialogShowing) return

        isDialogShowing = true
        val remainMs = (data.remainMilliseconds) ?: 0L
        val seconds = remainMs / 1000L

        val dialog = PromotionDialogHelper.showPromotionDialog(
            fragmentManager = childFragmentManager,
            amount = data.exchangeCoin?.toString() ?: "0",
            addAmount = if ((data.extraCoin ?: 0) > 0) "+${data.extraCoin}" else "",
            description = data.activityName ?: "",
            price = "$${data.price ?: ""}/$${data.originalPrice ?: ""}",
            layoutResId = R.layout.dialog_promotion1,
            countdownSeconds = seconds,
            buttonSvgaName = "sweep_button.svga",
            buttonEffectSvgaName = "tags_lucky.svga",
            remainingCount = (data.capableRechargeNum ?: 0) - (data.rechargeNum ?: 0),
            treasureBoxImageUrl = data.activityPic ?: data.activitySmallPic,
            promotionStartTimeMillis = System.currentTimeMillis(),
            onButtonClickListener = {
                CoinRechargeDialog().show(requireActivity().supportFragmentManager, "coin_recharge")
            }
        )

        // 监听弹窗关闭事件
        childFragmentManager.setFragmentResultListener("PromotionDialog_dismissed", this) { _, _ ->
            isDialogShowing = false
            childFragmentManager.clearFragmentResultListener("PromotionDialog_dismissed")
        }
    }

    private fun showPresentedCoinsDialog(data: GoodsInfo) {
        if (isDialogShowing) return

        isDialogShowing = true
        // 使用surplusMillisecond字段，如果没有则使用remainMilliseconds
        val remainMs = (data.surplusMillisecond ?: data.remainMilliseconds) ?: 0L
        val seconds = remainMs / 1000L

        val dialog = PromotionDialogHelper.showPromotionDialog(
            fragmentManager = childFragmentManager,
            amount = data.exchangeCoin?.toString() ?: "0",
            description = data.activityName ?: "",
            price = "$${data.price ?: ""}/$${data.originalPrice ?: ""}",
            layoutResId = R.layout.dialog_promotion,
            countdownSeconds = seconds,
            buttonSvgaName = "sweep_button.svga",
            buttonEffectSvgaName = "discount_tags.svga",
            promotionStartTimeMillis = System.currentTimeMillis(),
            onButtonClickListener = {
                CoinRechargeDialog().show(requireActivity().supportFragmentManager, "coin_recharge")
            }
        )

        // 监听弹窗关闭事件
        childFragmentManager.setFragmentResultListener("PromotionDialog_dismissed", this) { _, _ ->
            isDialogShowing = false
            childFragmentManager.clearFragmentResultListener("PromotionDialog_dismissed")
        }
    }
    // 使用CountdownManager管理HomeFragment的倒计时
    private fun startCountdownTimer(textView: TextView, totalMilliseconds: Long, activityType: CountdownManager.ActivityType, activityId: String) {
        countDownJob?.cancel()

        // 初始化倒计时状态
        CountdownManager.initOrUpdateCountdown(activityType, activityId, totalMilliseconds)

        // 启动倒计时
        countDownJob = CountdownManager.startCountdown(
            activityType = activityType,
            activityId = activityId,
            onTick = { remainingMillis ->
                ThreadUtils.runOnMain {
                    val remainingSeconds = remainingMillis / 1000
                    textView.text = TimeUtils.formatSecondsToTime(remainingSeconds)
                }
            },
            onFinish = {
                ThreadUtils.runOnMain {
                    textView.text = "00:00:00"
                }
            }
        )
    }

    private fun playSvgaAnim(svgaView: SVGAImageView, assetName: String) {
        val parser = SVGAParser(requireContext())
        parser.decodeFromAssets(assetName, object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: SVGAVideoEntity) {
                svgaView.setImageDrawable(SVGADrawable(videoItem))
                svgaView.startAnimation()
            }

            override fun onError() {}
        })
    }

}