package com.score.callmetest.ui.home

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.R
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.DisplayUtils
import android.graphics.Color
import android.widget.LinearLayout
import androidx.core.graphics.toColorInt
import com.score.callmetest.util.click
import androidx.core.graphics.drawable.toDrawable

class CountryFilterPopupWindow(
    context: Context,
    private val onCountrySelected: (String?) -> Unit
) : PopupWindow(context) {

    private lateinit var adapter: CountryFilterAdapter
    private var selectedCountryCode: String? = null

    init {
        setupPopupWindow()
        setupRecyclerView()
        loadCountries()
    }

    private fun setupPopupWindow() {
        val view = LayoutInflater.from(context).inflate(R.layout.bottom_sheet_country_filter, null)
        contentView = view

        // 计算屏幕宽度，减去左右 margin
        val displayMetrics = context.resources.displayMetrics
        val margin = (14 * displayMetrics.density).toInt()
        width = displayMetrics.widthPixels - 2 * margin
        height = ViewGroup.LayoutParams.WRAP_CONTENT

        isOutsideTouchable = true
        isFocusable = true
        elevation = 8f

        // 设置背景和动画
        //view.setBackgroundResource(R.color.white)
        // animationStyle = android.R.style.Animation_Dialog

        // 设置popup_country_filter背景（白色，圆角12dp，阴影）
        val popupLayout = view.findViewById<LinearLayout>(R.id.popup_country_filter)
        DrawableUtils.setShadowBackground(
            popupLayout,
            0xffffffff.toInt(), // 白色
            DisplayUtils.dp2pxInternal(12f).toFloat(), // 圆角12dp
            0x4259dfff, // 阴影色 #4259dfff
            DisplayUtils.dp2pxInternal(8f).toFloat(), // shadowRadius
            0f, // dx
            DisplayUtils.dp2pxInternal(4f).toFloat() // dy
        )
        setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
    }


    private fun setupRecyclerView() {
        val recyclerView = contentView.findViewById<RecyclerView>(R.id.rv_countries)
        recyclerView.layoutManager = GridLayoutManager(context, 3)
        recyclerView.addItemDecoration(object : RecyclerView.ItemDecoration() {
            private val space = (12 * context.resources.displayMetrics.density).toInt()
            override fun getItemOffsets(
                outRect: android.graphics.Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val position = parent.getChildAdapterPosition(view)
                val column = position % 3
                outRect.left = column * space / 3
                outRect.right = space - (column + 1) * space / 3
                if (position >= 3) {
                    outRect.top = space
                }
            }
        })
        
        adapter = CountryFilterAdapter { countryCode ->
            selectedCountryCode = countryCode
            onCountrySelected(countryCode)
            dismiss()
        }
        recyclerView.adapter = adapter
    }

    fun show(anchorView: View) {
        val margin = (14 * anchorView.context.resources.displayMetrics.density).toInt()
        // showAsDropDown会让popup顶部和anchorView底部对齐
        showAsDropDown(anchorView, margin, 5)
    }

    private fun loadCountries() {
        val countries = mutableListOf<CountryFilterItem>()
        countries.add(CountryFilterItem("ALL", "ALL", R.drawable.all))
        
        val regions = StrategyManager.strategyConfig?.broadcasterWallRegions ?: emptyList()
        regions.forEach { regionCode ->
            regionCode.let { code ->
                val countryName = CountryUtils.getCountryByIso(code)?.enName ?: "ALL"
                val iconRes = CountryUtils.getCountryIconResByIso(code)
                countries.add(CountryFilterItem(code, countryName, iconRes))
            }
        }
        adapter.updateData(countries)
    }
}

data class CountryFilterItem(
    val code: String,
    val name: String,
    val iconRes: Int
)
class CountryFilterAdapter(
    private val onCountrySelected: (String?) -> Unit
) : RecyclerView.Adapter<CountryFilterAdapter.ViewHolder>() {

    private var countries: List<CountryFilterItem> = emptyList()

    fun updateData(newCountries: List<CountryFilterItem>) {
        countries = newCountries
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_country_filter, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val country = countries[position]
        holder.bind(country)
    }

    override fun getItemCount(): Int = countries.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivFlag: ImageView = itemView.findViewById(R.id.iv_flag)
        private val tvFlagCountryName: TextView = itemView.findViewById(R.id.tv_flag_country_name)
        private val itemLayout: LinearLayout = itemView.findViewById(R.id.item_country_filter)

        fun bind(country: CountryFilterItem) {
            ivFlag.setImageResource(country.iconRes)
            tvFlagCountryName.text = country.name
            // 设置item_country_filter背景 #fff3f5fa 圆角13dp
            DrawableUtils.setRoundRectBackground(
                itemLayout,
                "#fff3f5fa".toColorInt(),
                DisplayUtils.dp2pxInternal(13f).toFloat()
            )
            itemView.click {
                onCountrySelected(country.code)
            }
        }
    }
}