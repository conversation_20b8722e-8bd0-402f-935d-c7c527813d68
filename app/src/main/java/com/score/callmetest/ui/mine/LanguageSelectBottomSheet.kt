package com.score.callmetest.ui.mine

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.util.click

class LanguageSelectBottomSheet(
    private val onLanguageSelected: (String) -> Unit
) : BottomSheetDialogFragment() {

    private val languages = listOf("简体中文", "English")
    private val languageCodes = listOf("zh", "en")

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.bottom_sheet_language_select, container, false)
        val zh = view.findViewById<TextView>(R.id.tv_language_zh)
        val en = view.findViewById<TextView>(R.id.tv_language_en)
        zh.click {
            onLanguageSelected(languageCodes[0])
            dismiss()
        }
        en.click {
            onLanguageSelected(languageCodes[1])
            dismiss()
        }
        return view
    }
} 