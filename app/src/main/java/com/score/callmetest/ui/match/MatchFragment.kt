package com.score.callmetest.ui.match

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.score.callmetest.databinding.FragmentDashboardBinding
import com.score.callmetest.ui.base.BaseFragment

class MatchFragment : BaseFragment<FragmentDashboardBinding, MatchViewModel>() {
    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentDashboardBinding {
        return FragmentDashboardBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = MatchViewModel::class.java

    override fun initView() {
        val textView: TextView = binding.textDashboard
        viewModel.text.observe(viewLifecycleOwner) {
            textView.text = it
        }
    }

}