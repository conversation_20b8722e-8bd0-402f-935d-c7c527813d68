package com.score.callmetest.ui.mine.blockList

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.score.callmetest.databinding.FragmentFollowTopBinding
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.util.ClickUtils

class BlockListContainerFragment : BaseFragment<FragmentFollowTopBinding, BlockListViewModel>() {

    private lateinit var pagerAdapter: BlockListPagerAdapter

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentFollowTopBinding {
        return FragmentFollowTopBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = BlockListViewModel::class.java

    override fun initView() {
        setupTabLayout()
        setupViewPager()
        // 默认选中第一个Tab
        binding.viewPager.post {
            binding.viewPager.setCurrentItem(0, false)
            binding.tabFollow.getTabAt(0)?.select()
        }
    }

    override fun initListener() {
        super.initListener()
        // 返回按钮点击事件
        ClickUtils.setOnIsolatedClickListener(binding.btnReturn) {
            activity?.onBackPressed()
        }
    }

    private fun setupTabLayout() {
        binding.tabFollow.addTab(binding.tabFollow.newTab().apply {
            customView = createCustomTabView("BlockList", selected = true)
        })
    }

    private fun setupViewPager() {
        pagerAdapter = BlockListPagerAdapter(this)
        binding.viewPager.adapter = pagerAdapter
        binding.viewPager.offscreenPageLimit = 1
    }

    private fun createCustomTabView(text: String, selected: Boolean): View {
        val view = layoutInflater.inflate(com.score.callmetest.R.layout.tab_custom, null)
        val textView = view.findViewById<android.widget.TextView>(com.score.callmetest.R.id.tab_text)
        val indicator = view.findViewById<View>(com.score.callmetest.R.id.tab_indicator)
        textView.text = text
        textView.textSize = if (selected) 22f else 15f
        textView.paint.isFakeBoldText = selected
        indicator.visibility = if (selected) View.VISIBLE else View.GONE
        return view
    }

    private inner class BlockListPagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {
        override fun getItemCount(): Int = 1
        override fun createFragment(position: Int): Fragment = BlockListFragment()
    }
} 