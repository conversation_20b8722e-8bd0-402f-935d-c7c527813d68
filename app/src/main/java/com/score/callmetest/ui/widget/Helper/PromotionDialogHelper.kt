package com.score.callmetest.ui.widget

import androidx.fragment.app.FragmentManager

/**
 * 促销弹窗辅助类，方便在任何地方调用显示促销弹窗
 * 目前有三个xml。1、dialog_promotion.xml 2、dialog_promotion2.xml 3、dialog_promotion_new_user.xml
 * buttonEffectSvgaName 有tags_lucky.svga； 以及discount_tags.svga
 */
class PromotionDialogHelper {
    companion object {
        /**
         * 显示促销弹窗
         *
         * @param fragmentManager FragmentManager
         * @param amount 金额文本
         * @param addAmount 额外金额文本（带+号）
         * @param description 描述文本
         * @param price 价格文本，格式为"当前价格/原价"，例如"$9.99/$19.99"
         * @param layoutResId 布局资源ID
         * @param countdownSeconds 倒计时秒数
         * @param buttonSvgaName 按钮SVGA动画名称
         * @param buttonEffectSvgaName 按钮特效SVGA动画名称
         * @param remainingCount 剩余数量
         * @param treasureBoxImageUrl 宝箱图片URL，如果为空则使用默认图片
         * @param onButtonClickListener 按钮点击回调
         *  @param promotionStartTimeMillis  活动开始时间戳（毫秒）
         */
        fun showPromotionDialog(
            fragmentManager: FragmentManager,
            amount: String,
            description: String,
            price: String = "$9.99/$19.99",
            layoutResId: Int = com.score.callmetest.R.layout.dialog_promotion1,
            countdownSeconds: Long = 30,
            buttonSvgaName: String = "sweep_button.svga",
            buttonEffectSvgaName: String = "tags_lucky.svga",
            onButtonClickListener: (() -> Unit)? = null,
            addAmount: String = "",
            remainingCount: Int = 2,
            treasureBoxImageUrl: String? = null,
            promotionStartTimeMillis: Long? = null // 新增参数
        ) {
            val dialog = PromotionDialogFragment.newInstance(
                layoutResId,
                amount,
                description,
                price,
                countdownSeconds,
                buttonSvgaName,
                buttonEffectSvgaName,
                onButtonClickListener,
                addAmount,
                remainingCount,
                treasureBoxImageUrl,
                promotionStartTimeMillis // 传递
            )
            dialog.show(fragmentManager, "PromotionDialog")
        }
    }
} 