package com.score.callmetest.ui.widget

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.databinding.DialogPaymentMethodSelectBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.widget.adapter.PaymentMethodAdapter
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

class PaymentMethodDialog(
    private val goodsInfo: GoodsInfo,
    private val onPaymentMethodSelected: (String) -> Unit
) : BottomSheetDialogFragment() {

    private var _binding: DialogPaymentMethodSelectBinding? = null
    private val binding get() = _binding!!
    private lateinit var adapter: PaymentMethodAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = DialogPaymentMethodSelectBinding.inflate(inflater, container, false)
        setupRecyclerView()
        setupButtons()
        setupProductInfo()
        loadPaymentMethods()
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun setupRecyclerView() {
        binding.rvPaymentMethods.layoutManager = LinearLayoutManager(requireContext())
        adapter = PaymentMethodAdapter(emptyList(), goodsInfo) { payChannel ->
            if (payChannel == PaymentMethodManager.PAY_CHANNEL_GP) {
                // 可在此处直接处理 GP 支付逻辑，或通过回调让外部处理
                onPaymentMethodSelected("GP")
            } else {
                onPaymentMethodSelected(payChannel)
            }
            dismiss()
        }
        binding.rvPaymentMethods.adapter = adapter
    }

    private fun setupButtons() {
        binding.layoutProductInfo.background = DrawableUtils.createRoundRectDrawableDp("#F3F5FAFF".toColorInt(), 12f)
        GlobalManager.setViewRoundBackground(binding.btnNext, "#7DF7EC".toColorInt())
        binding.btnNext.click {
            dismiss()
        }
    }

    private fun setupProductInfo() {
        // 显示商品信息
        val coinAmount = (goodsInfo.exchangeCoin ?: 0) + (goodsInfo.extraCoin ?: 0)
        val extraCoin = goodsInfo.extraCoin ?: 0

        val productDesc = if (extraCoin > 0) {
            "${goodsInfo.exchangeCoin ?: 0} Coins +$extraCoin Coins"
        } else {
            "${coinAmount} Coins"
        }

        binding.tvProductDesc.text = productDesc
        binding.tvProductPrice.text = "$${goodsInfo.price ?: 0.0}"
    }

    private fun loadPaymentMethods() {
        // 先获取支付渠道列表，然后计算折扣
        CoroutineScope(Dispatchers.Main).launch {
            val payChannelList = RechargeManager.ensurePayChannelListLoaded()
            val availableChannels = PaymentMethodManager.getAvailablePayChannels()
            val defaultMethod = PaymentMethodManager.getDefaultPaymentMethod()

            val paymentMethods = availableChannels.map { channel ->
                val discount = calculateChannelDiscount(channel, payChannelList)
                PaymentMethodItem(
                    payChannel = channel,
                    displayName = PaymentMethodManager.getPaymentChannelDisplayName(channel),
                    iconRes = PaymentMethodManager.getPaymentChannelIcon(channel),
                    discount = discount,
                    isSelected = channel == defaultMethod
                )
            }
            adapter.updateData(paymentMethods)
        }
    }

    /**
     * 根据商品类型和支付渠道计算折扣
     */
    private fun calculateChannelDiscount(payChannel: String, payChannelList: List<com.score.callmetest.network.PayChannelItem>?): Int {
        val channelItem = payChannelList?.find { it.payChannel == payChannel }
        if (channelItem == null) return 0
        return when (goodsInfo.type) {
            "0" -> channelItem.presentCoinRatio ?: 0  // 普通商品
            "1" -> channelItem.promotionPresentCoinRatio ?: 0  // 促销商品
            "2" -> {  // 活动商品：取thirdpartyCoinPercent和promotionPresentCoinRatio的最大值
                val thirdpartyCoinPercent = goodsInfo.thirdpartyCoinPercent ?: 0
                val promotionPresentCoinRatio = channelItem.promotionPresentCoinRatio ?: 0
                maxOf(thirdpartyCoinPercent, promotionPresentCoinRatio)
            }
            "3" -> channelItem.presentCoinRatio ?: 0  // 订阅商品
            else -> channelItem.presentCoinRatio ?: 0  // 默认使用普通商品的比例
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?) = super.onCreateDialog(savedInstanceState).apply {
        setOnShowListener {
            val bottomSheet = (this as? BottomSheetDialog)
                ?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
    }
}

data class PaymentMethodItem(
    val payChannel: String,
    val displayName: String,
    val iconRes: Int,
    val discount: Int,
    val isSelected: Boolean
)