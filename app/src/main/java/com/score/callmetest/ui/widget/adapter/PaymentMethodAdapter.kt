package com.score.callmetest.ui.widget.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.databinding.ItemPaymentMethodBinding
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.widget.PaymentMethodItem
import com.score.callmetest.util.click

class PaymentMethodAdapter(
    private var paymentMethods: List<PaymentMethodItem>,
    private val goodsInfo: GoodsInfo,
    private val onItemClick: (String) -> Unit
) : RecyclerView.Adapter<PaymentMethodAdapter.ViewHolder>() {

    class ViewHolder(val binding: ItemPaymentMethodBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemPaymentMethodBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = paymentMethods[position]
        
        holder.binding.apply {
            ivIcon.setImageResource(item.iconRes)
            tvName.text = item.displayName

            // 优惠信息
            if (item.discount > 0) {
                tvDiscount.text = "+${item.discount}% More Coins"
                tvDiscount.visibility = android.view.View.VISIBLE
            } else {
                tvDiscount.visibility = android.view.View.GONE
            }

            // 选中状态
            if (item.isSelected) {
                ivSelected.visibility = android.view.View.VISIBLE
            } else {
                ivSelected.visibility = android.view.View.GONE
            }

            itemLayout.click {
                onItemClick(item.payChannel)
            }
        }
    }

    override fun getItemCount(): Int = paymentMethods.size

    fun updateData(newPaymentMethods: List<PaymentMethodItem>) {
        paymentMethods = newPaymentMethods
        notifyDataSetChanged()
    }
} 