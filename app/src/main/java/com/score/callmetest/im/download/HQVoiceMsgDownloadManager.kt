package com.score.callmetest.im.download

import android.annotation.SuppressLint
import android.text.TextUtils
import android.util.Log
import androidx.fragment.app.Fragment
import com.score.callmetest.CallmeApplication
import com.score.callmetest.entity.MessageEvents
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.im.callback.ImOnReceiveMessageListener
import com.score.callmetest.util.EventBus
import io.rong.imlib.IRongCallback
import io.rong.imlib.RongIMClient
import io.rong.imlib.common.NetUtils
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import io.rong.message.HQVoiceMessage
import okhttp3.internal.notify
import okhttp3.internal.wait
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future


@SuppressLint("LogNotTimber")
object HQVoiceMsgDownloadManager {
    private val TAG: String = HQVoiceMsgDownloadManager::class.java.getSimpleName()
//    private var mContext: Context? = null

    // 语音自动下载队列
    private val autoDownloadQueue: HQAutoDownloadQueue = HQAutoDownloadQueue()
    private var networkMonitor: NetworkMonitor? = null
    private var executorService: ExecutorService? = null
    private var future: Future<*>? = null
    private var errorList: MutableList<HQAutoDownloadEntry?>? = null
//    private val writePermission = arrayOf<String>(Manifest.permission.WRITE_EXTERNAL_STORAGE)
    private val onReceiveMessageWrapperListener:  ImOnReceiveMessageListener=
        object : ImOnReceiveMessageListener {

            override fun onReceivedMessage(message: Message?, profile: ReceivedProfile?) {
                val offline = profile?.isOffline?:false
                if (!offline && message?.content is HQVoiceMessage) {
//                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU
//                        && PermissionUtils.hasAllPermissions(CallmeApplication.context,writePermission)
//                    ) {
                    // 写到应用内不需要权限---接收到语音就直接下载队列
                    enqueue(HQAutoDownloadEntry(message, DownloadPriority.NORMAL))
                }
            }

        }

    fun init() {
        // 网络监控
        networkMonitor = NetworkMonitor()
        // 下载线程
        executorService = Executors.newSingleThreadExecutor()
        // 错误信息
        errorList = ArrayList()
        // 直接挂起下载等待语音
        downloadHQVoiceMessage()
        // 注册信息接收器
        RongCloudManager.addOnReceiveMessageListener(onReceiveMessageWrapperListener)
    }

    fun clear(){
        networkMonitor?.clear()
    }

    /**
     * 入队等待下载
     */
    fun enqueue(fragment: Fragment?, autoDownloadEntry: HQAutoDownloadEntry?) {
        if (autoDownloadEntry == null) {
            return
        }

        val message: Message = autoDownloadEntry.message
        if (message.content !is HQVoiceMessage || (ifMsgInHashMap(message) && fragment != null)) {
            return
        }

        //  下载保存至私有目录
        val hqVoiceMessage = message.content as HQVoiceMessage

        // 需要入队的message本地Uri已有--已经下载--return
        if (!(hqVoiceMessage.localPath == null
                    || TextUtils.isEmpty(hqVoiceMessage.localPath.toString())))  return

        // 同步--入队
        synchronized(autoDownloadQueue) {
            val isEmpty: Boolean = autoDownloadQueue.isEmpty
            // 加入队列等待下载
            autoDownloadQueue.enqueue(autoDownloadEntry)
            if (isEmpty) {
                // 下载队列为空--不需要等待
                autoDownloadQueue.notify()
            }
            // 当前下载器空闲 && 网络可用
            if (future?.isDone == true && NetUtils.isNetWorkAvailable(CallmeApplication.context)) {
                downloadHQVoiceMessage()
            }
        }
    }

    /**
     * 入队等待下载
     */
    fun enqueue(autoDownloadEntry: HQAutoDownloadEntry?) {
        enqueue(null, autoDownloadEntry)
    }

    /**
     * 取消下载任务
     */
    private fun dequeue(): Message? {
        return autoDownloadQueue.dequeue()
    }


    /**
     * 根据uid直接移除队列中相应message
     * @param [uid] uid
     */
    private fun removeUidInHashMap(uid: String?) {
        autoDownloadQueue.autoDownloadEntryHashMap.remove(uid)
    }

    /**
     * message是否已经在下载队列中
     */
    private fun ifMsgInHashMap(message: Message?): Boolean {
        return autoDownloadQueue.ifMsgInHashMap(message)
    }

    /**
     * 根据messageId从队列中拿出对应的HQAutoDownloadEntry
     */
    private fun getMsgEntry(message: Message?): HQAutoDownloadEntry? {
        if (message == null) {
            return null
        }
        var autoDownloadEntry: HQAutoDownloadEntry? = null
        if (autoDownloadQueue.autoDownloadEntryHashMap.containsKey(message.uId)) {
            autoDownloadEntry =
                autoDownloadQueue.autoDownloadEntryHashMap.get(message.uId)
        }
        return autoDownloadEntry
    }

    /**
     * 开始下载
     */
    private fun downloadHQVoiceMessage() {
        future =
            executorService?.submit {
                while (true) {
                    synchronized(autoDownloadQueue) {
                        if (autoDownloadQueue.isEmpty) {
                            try {
                                // 等待队列中入队数据
                                autoDownloadQueue.wait()
                            } catch (e: InterruptedException) {
                                Log.e(TAG, "downloadHQVoiceMessage e:$e")
                                Thread.currentThread().interrupt()
                            }
                        }
                    }

                    // 出队
                    val message: Message? = dequeue()
                    RongCloudManager.downloadMediaMessage(
                            message,
                            object : IRongCallback.IDownloadMediaMessageCallback {
                                override fun onSuccess(message: Message) {
                                    val localPath = (message.content as HQVoiceMessage).localPath
                                    Log.d(TAG, "downloadMediaMessage success--$localPath")
                                    // 下载成功--移除对应错误信息
                                    errorList?.remove(getMsgEntry(message))
                                    // 下载成功--移除队列中对应数据
                                    removeUidInHashMap(message.uId)
                                    // 如果中chat页面--提醒更新
                                    EventBus.post(MessageEvents.AudioDownloadOk(message.messageId.toString(),localPath))
                                }

                                override fun onProgress(message: Message?, progress: Int) {
                                    Log.d(TAG, "downloadMediaMessage onProgress--$progress")
                                }

                                override fun onError(message: Message?, code: RongIMClient.ErrorCode) {
                                    if(errorList == null) return
                                    val msgEntry = getMsgEntry(message)
                                    if (!errorList!!.contains(msgEntry)) {
                                        errorList!!.add(msgEntry)
                                        Log.i(TAG, ("onError = " + code.value + " errorList size = " + errorList!!.size))
                                    }
                                }

                                override fun onCanceled(message: Message?) {
                                    // do nothing
                                }
                            })
                }
            }
    }

    /**
     * 暂停下载--语音不需要
     *
     * 网络或者权限等问题导致下载中断--会在errorList中记录
     *
     */
    fun pauseDownloadService() {
        // do nothing
    }

    /**
     * 继续下载
     */
    fun resumeDownloadService() {
        // 没有下载错误的--不需要重试了
        if (errorList.isNullOrEmpty()) {
            return
        }
        if (future?.isDone == true && NetUtils.isNetWorkAvailable(CallmeApplication.context)) {
            downloadHQVoiceMessage()
        }

        // 下载出现错误的再次入队
        for (i in errorList!!.indices.reversed()) {
            enqueue(errorList!![i])
        }
    }

}