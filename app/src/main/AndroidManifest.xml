<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="CoarseFineLocation">

    <!-- SDK 必须使用的权限 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
<!--    <uses-permission android:name="android.permission.BLUETOOTH" />-->

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- 对于 Android 12.0 及以上设备，还需要添加如下权限： -->
<!--    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />-->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"
        tools:ignore="CoarseFineLocation" />

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- App 需要使用的部分权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <!-- Android 13+ 相册权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"
        tools:ignore="SelectedPhotoAccess" />

    <!-- 悬浮窗权限 -->
<!--    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />-->

<!--    &lt;!&ndash; 后台弹出界面权限（Android 10+ 需要） &ndash;&gt;-->
<!--    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />-->
<!--    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />-->
<!--    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />-->
<!--    <uses-permission android:name="android.permission.REQUEST_BACKGROUND_ACTIVITY_START" />-->

    <uses-permission android:name="com.android.vending.BILLING" />



    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <application
        android:name=".CallmeApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Callme.Immersive"
        tools:targetApi="31">

        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Callme.Immersive">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.web.WebViewActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.profile.ProfileActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <activity
            android:name="com.score.callmetest.ui.preview.MultiImagePreviewActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.score.callmetest.ui.videocall.VideoCallActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:keepScreenOn="true"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.score.callmetest.ui.main.BroadcasterGiftListActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:keepScreenOn="true" />

        <activity
            android:name="com.score.callmetest.ui.mine.SettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:keepScreenOn="true" />

        <activity android:name="com.score.callmetest.ui.mine.about.AboutUsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.score.callmetest.ui.chat.ChatActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name="com.score.callmetest.ui.main.CoinStoreActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:keepScreenOn="true" />

        <activity
            android:name="com.score.callmetest.ui.mine.follow.FollowActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
    </application>

</manifest>