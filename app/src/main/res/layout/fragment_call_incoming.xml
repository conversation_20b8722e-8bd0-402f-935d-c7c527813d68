<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/photo_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 底部渐变遮罩 -->
    <View
        android:id="@+id/bottom_shadow"
        android:layout_width="match_parent"
        android:layout_height="460dp"
        android:layout_gravity="bottom"
        android:orientation="vertical" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="260dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="32dp"
        android:background="@android:color/transparent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <!-- 用户名和标签 -->
        <TextView
            android:id="@+id/tv_nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="CCHUBBY"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 标签和国家 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/age_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp">

                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:src="@drawable/chat_girl" />

                <TextView
                    android:id="@+id/tv_age"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="22"
                    android:textColor="@color/age_color"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:typeface="sans" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_country"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:text="India"
                android:textColor="@color/country_color"
                android:textSize="12sp"
                android:textStyle="bold"
                android:typeface="sans" />
        </LinearLayout>

        <!-- 提示 -->
        <TextView
            android:id="@+id/tv_free_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="24dp"
            android:autoLink="none"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:lineSpacingExtra="2dp"
            android:maxLines="2"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:text="You'll be charged free for the first minute"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="18sp" />

        <!-- 接听和挂断按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <com.score.callmetest.ui.widget.AlphaImageView
                android:id="@+id/btn_reject"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginEnd="32dp"
                android:background="@android:color/transparent"
                android:contentDescription="hang up"
                android:padding="18dp"
                android:scaleType="centerInside"
                android:src="@drawable/ic_hangup" />

            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/btn_accept"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:contentDescription="answer call" />
        </LinearLayout>
    </LinearLayout>


</FrameLayout> 