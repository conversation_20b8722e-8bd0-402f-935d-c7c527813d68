<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="74dp"
    android:layout_height="85dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="13dp"
        android:background="@drawable/bg_gift_item">

        <TextView
            android:id="@+id/tv_gift_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="44dp"
            android:textAlignment="center"
            android:text="x10"
            android:textStyle="bold"
            android:textColor="#FF000000"
            android:textSize="12sp"
            android:background="@android:color/transparent" />

    </FrameLayout>
    <ImageView
        android:id="@+id/iv_gift"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="top|center_horizontal"
        android:scaleType="centerCrop"
        android:src="@drawable/gift_placehold" />

</FrameLayout> 