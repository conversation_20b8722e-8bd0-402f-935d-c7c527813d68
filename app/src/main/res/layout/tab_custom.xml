<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <View
        android:id="@+id/tab_indicator"
        android:layout_width="0dp"
        android:layout_height="4dp"
        android:layout_alignEnd="@+id/tab_text"
        android:layout_alignBottom="@+id/tab_text"
        android:background="@drawable/tab_indicator_bg"
        android:layout_marginBottom="2dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tab_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@android:color/black"
        android:textSize="20sp"
        android:textStyle="normal" />

</RelativeLayout>