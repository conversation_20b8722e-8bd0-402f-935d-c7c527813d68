<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:clipChildren="false"
    android:clipToPadding="false">
    <!-- 关闭按钮 - 使用约束布局精确定位 -->
    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/promotion_dialog_cancel"
        app:layout_constraintEnd_toEndOf="@id/promotion"
        app:layout_constraintBottom_toTopOf="@+id/promotion"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintHorizontal_bias="1"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="15dp" />

    <!-- 主内容容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/promotion"
        android:layout_width="336dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_promotion"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <!-- 宝箱图片 -->
        <ImageView
            android:id="@+id/iv_treasure_box"
            android:layout_width="120dp"
            android:layout_height="120dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/promotion_box" />

        <!-- 金额文本 -->
        <TextView
            android:id="@+id/tv_amount"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:textColor="#FFFFF7F2"
            android:textSize="30sp"
            android:textStyle="bold|italic"
            android:gravity="center"
            android:includeFontPadding="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_treasure_box"
            android:layout_marginTop="-24dp"
            tools:text="500" />

        <!-- 描述文本 -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="13dp"
            android:layout_marginEnd="24dp"
            android:gravity="center"
            android:textColor="#FFFFF7F2"
            android:textStyle="bold|italic"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_amount"
            tools:text="A Wonderful Discount for New User!" />

        <!-- 按钮容器 -->
        <FrameLayout
            android:id="@+id/fl_button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_marginStart="50dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="50dp"
            android:layout_marginBottom="32dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_description">

            <!-- SVGA按钮动画 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/svga_button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_gravity="center" />

            <!-- 按钮文字 -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/price_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp"
                android:paddingVertical="3dp">

                <TextView
                    android:id="@+id/tv_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$0.99"
                    android:textColor="#FF763628"
                    android:textStyle="bold"
                    android:textSize="17sp" />

                <TextView
                    android:id="@+id/tv_old_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="5dp"
                    android:text="$1.99"
                    android:textColor="#80763628"
                    android:textSize="14sp" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <!-- 按钮右上角动画 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/svga_button_effect"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:layout_gravity="end|top"
                android:layout_marginTop="-16dp"
                android:layout_marginEnd="-16dp" />

            <!-- 倒计时 -->
            <TextView
                android:id="@+id/tv_countdown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|top"
                android:paddingStart="8dp"
                android:paddingTop="5dp"
                android:paddingEnd="8dp"
                android:paddingBottom="5dp"
                android:layout_marginTop="-10dp"
                android:textStyle="bold"
                android:textColor="#FFFFF7F2"
                android:textSize="12sp"
                tools:text="12:30:25" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
