<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="29dp"
        android:background="@drawable/bg_dialog_rounded">

        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/login_dialog_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="32dp"
            android:paddingTop="73dp"
            android:paddingBottom="25dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:text="调试模式 - 设备ID设置"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:fontFamily="@font/roboto_medium_xml"
                android:gravity="center"
                android:lineHeight="17dp"
                android:text="开启后将使用随机设备ID进行登录，方便调试促销接口"
                android:textColor="#808080"
                android:textSize="16sp"
                android:textStyle="normal" />

            <!-- 开关区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="启用随机设备ID"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <Switch
                    android:id="@+id/switch_debug_device_id"
                    style="@style/NoRippleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="5dp"
                    android:thumb="@drawable/switch_thumb"
                    android:track="@drawable/switch_track"  />

            </LinearLayout>

            <!-- 当前设备ID显示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="真实设备ID:"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_real_device_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@color/bg_coin_card_pressed"
                    android:padding="8dp"
                    android:text=""
                    android:textColor="#666666"
                    android:textSize="12sp"
                    android:textIsSelectable="true" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="当前使用的设备ID:"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_current_device_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@color/bg_coin_card_pressed"
                    android:padding="8dp"
                    android:text=""
                    android:textColor="#666666"
                    android:textSize="12sp"
                    android:textIsSelectable="true" />

            </LinearLayout>

            <!-- 生成新设备ID按钮 -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_generate_new_id"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_btn_rounded_blue"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="生成新的随机设备ID"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <!-- 确认按钮 -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_confirm"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_btn_rounded_blue"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="确认"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <!-- 取消按钮 -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/bg_btn_rounded_black"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="取消"
                    android:textColor="#FFFFFF"
                    android:textSize="18sp"
                    android:textStyle="bold" />

            </com.score.callmetest.ui.widget.AlphaLinearLayout>

        </LinearLayout>

    </FrameLayout>

    <ImageView
        android:id="@+id/emoji"
        android:layout_width="wrap_content"
        android:layout_height="91dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/emoji_laugh" />

</FrameLayout>
