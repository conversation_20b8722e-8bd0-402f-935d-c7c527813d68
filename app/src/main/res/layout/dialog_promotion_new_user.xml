<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:clipChildren="false"
    android:clipToPadding="false">
    <!-- 关闭按钮 - 使用约束布局精确定位 -->
    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/promotion_dialog_cancel"
        app:layout_constraintEnd_toEndOf="@id/promotion"
        app:layout_constraintBottom_toTopOf="@+id/promotion"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintHorizontal_bias="1"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="15dp" />

    <!-- 主内容容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/promotion"
        android:layout_width="336dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_promotion"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <!-- 宝箱图片 -->
        <ImageView
            android:id="@+id/iv_treasure_box"
            android:layout_width="120dp"
            android:layout_height="120dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/promotion_box" />

        <!-- 金额文本 -->
        <TextView
            android:id="@+id/tv_amount"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:textColor="#FFFFF7F2"
            android:textSize="30sp"
            android:textStyle="bold|italic"
            android:gravity="center"
            android:includeFontPadding="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_treasure_box"
            android:layout_marginTop="-24dp"
            tools:text="500" />



        <!-- 按钮容器 -->
        <FrameLayout
            android:id="@+id/fl_button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_marginStart="50dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="50dp"
            android:layout_marginBottom="32dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_amount">

            <!-- SVGA按钮动画 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/svga_button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_gravity="center" />

            <!-- 按钮文字 -->
            <TextView
                android:id="@+id/tv_button_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Claim"
                android:textColor="#FF763628"
                android:textSize="17sp"
                android:textStyle="bold" />

            <!-- 按钮右上角动画 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/svga_button_effect"
                android:layout_width="75dp"
                android:layout_height="31dp"
                android:layout_gravity="end|top"
                android:layout_marginTop="-20dp"
                android:layout_marginEnd="-5dp" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
