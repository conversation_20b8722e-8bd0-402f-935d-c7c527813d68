<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_broadcaster_gift">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Gifts Received"
        android:textColor="#fff"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 返回按钮 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/iv_back"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/info_btn_back"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="12dp"
        android:layout_marginTop="32dp" />

    <!-- 礼物网格列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_gift_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingBottom="13dp"
        android:paddingStart="21dp"
        android:paddingEnd="21dp"
        android:layout_marginTop="310dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 