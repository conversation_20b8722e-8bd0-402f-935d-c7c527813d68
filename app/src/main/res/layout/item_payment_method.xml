<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    card_view:cardCornerRadius="16dp"
    card_view:cardElevation="2dp"
    android:background="@android:color/transparent">

    <com.score.callmetest.ui.widget.AlphaLinearLayout
        android:id="@+id/item_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <!-- 支付方式图标 -->
        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/googleg_disabled_color_18"
            android:scaleType="centerInside" />

        <!-- 名称、优惠、标签 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="12dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Credit Card"
                    android:textColor="#222"
                    android:textSize="16sp"
                    android:textStyle="bold"/>
                <TextView
                    android:id="@+id/tv_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Recommend"
                    android:textColor="#FF9800"
                    android:textSize="12sp"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:layout_marginStart="8dp"
                    android:visibility="gone"/>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_discount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+50% More Coins"
                android:textColor="#FF9800"
                android:textSize="14sp"
                android:layout_marginTop="4dp"
                android:visibility="gone"/>
        </LinearLayout>

        <!-- 选择框 -->
        <ImageView
            android:id="@+id/iv_selected"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:visibility="gone"/>
    </com.score.callmetest.ui.widget.AlphaLinearLayout>
</androidx.cardview.widget.CardView>
