<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <!-- 消息弹窗容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/messageContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="14dp"
        android:layout_marginTop="44dp"
        android:background="@drawable/shape_message_popup_bg"
        android:clickable="true"
        android:focusable="true"
        android:paddingHorizontal="14dp"
        android:paddingVertical="16dp"
        android:elevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 头像 -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_avatar"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/placeholder" />

        <!-- 昵称 -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_nickname"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="15sp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintEnd_toStartOf="@id/iv_right_icon"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/tv_content"
            tools:text="Kathleen" />

        <!-- 消息内容 -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="8dp"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_gray"
            android:textSize="14sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/iv_right_icon"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_nickname"
            tools:text="Nice to meet you! How are you doing today?" />

        <!-- 右侧图标 -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_right_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/icon_message"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
