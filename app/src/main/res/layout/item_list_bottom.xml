<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingVertical="20dp">

    <View
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:background="@drawable/shape_dot" />

    <TextView
        android:id="@+id/tv_bottom_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:text="@string/str_bottom"
        android:textColor="@color/call_text_gray"
        android:textSize="12sp" />
        
    <View
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:background="@drawable/shape_dot" />

</LinearLayout> 