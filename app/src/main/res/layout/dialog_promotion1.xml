<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:clipChildren="false"
    android:clipToPadding="false">
    <!-- 关闭按钮 - 使用约束布局精确定位 -->
    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/promotion_dialog_cancel"
        app:layout_constraintEnd_toEndOf="@id/promotion"
        app:layout_constraintBottom_toTopOf="@+id/promotion"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintHorizontal_bias="1"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="15dp" />

    <!-- 主内容容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/promotion"
        android:layout_width="336dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_promotion"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <!-- 活动图片 -->
        <ImageView
            android:id="@+id/iv_treasure_box"
            android:layout_width="265dp"
            android:layout_height="125dp"
            android:layout_marginTop="20dp"
            android:scaleType="centerCrop"
            android:contentDescription="Treasure Box"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/promotion_action_src" />
        <LinearLayout
            android:id="@+id/line_sum"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="20dp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_treasure_box">
            <!-- 金额文本 -->
            <TextView
                android:id="@+id/tv_amount"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:textColor="#FFFFF7F2"
                android:textSize="30sp"
                android:textStyle="bold|italic"
                tools:text="500" />
            <TextView
                android:id="@+id/tv_add_amount"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:textColor="#FF54F6FF"
                android:textSize="30sp"
                android:textStyle="bold|italic"
                android:gravity="center"
                tools:text="+50" />
            <ImageView
                android:layout_width="29dp"
                android:layout_height="29dp"
                android:layout_marginStart="4dp"
                android:layout_gravity="center"
                android:src="@drawable/coin"/>

        </LinearLayout>



        <!-- 描述文本 -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="7dp"
            android:layout_marginEnd="24dp"
            android:gravity="center"
            android:textColor="#FFFFF7F2"
            android:textStyle="bold|italic"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/line_sum"
            tools:text="Congrats! Surprise Discount!" />

        <!-- 按钮容器 -->
        <FrameLayout
            android:id="@+id/fl_button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_marginStart="50dp"
            android:layout_marginTop="22dp"
            android:layout_marginEnd="50dp"
            android:layout_marginBottom="32dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_description">

            <!-- SVGA按钮动画 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/svga_button"
                android:layout_width="228dp"
                android:layout_height="56dp"
                android:layout_gravity="center" />

            <!-- 按钮文字 -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/price_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp"
                android:paddingVertical="3dp">

                <TextView
                    android:id="@+id/tv_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$0.99"
                    android:textColor="#FF763628"
                    android:textStyle="bold"
                    android:textSize="17sp" />

            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <!-- 倒计时 -->
            <TextView
                android:id="@+id/tv_countdown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|top"
                android:paddingStart="8dp"
                android:paddingTop="5dp"
                android:paddingEnd="8dp"
                android:paddingBottom="5dp"
                android:layout_marginTop="-10dp"
                android:layout_marginEnd="3dp"
                android:textColor="#FFFFF7F2"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="12:30:25" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_account_down"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|top"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="18dp"
            android:textColor="#FFFFF7F2"
            android:textSize="12sp"
            android:textStyle="bold"
            tools:text="Only 2 left"
            app:layout_constraintTop_toBottomOf="@+id/fl_button_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>



    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
