<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_top_bg"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="Payment Method"
            android:textColor="#000"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 商品信息卡片 -->
        <LinearLayout
            android:id="@+id/layout_product_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="16dp"
            android:background="#F5F7FA"
            android:clipToOutline="true"
            android:elevation="2dp"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Item"
                    android:textColor="#A0A4AB"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Price"
                    android:textColor="#A0A4AB"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_product_desc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="150 Coins +30 Coins"
                    android:textColor="#222"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_product_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$240"
                    android:textColor="#222"
                    android:textSize="16sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- 支付方式列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_payment_methods"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- Next 按钮 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_next"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:gravity="center"
            android:paddingVertical="15dp"
            android:text="Next"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>
</ScrollView> 