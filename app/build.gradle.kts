import com.android.build.api.dsl.Packaging
import org.gradle.internal.declarativedsl.parsing.main
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id("org.jetbrains.kotlin.plugin.serialization") version "2.0.21"
    id("org.jetbrains.kotlin.kapt")
    id("kotlin-parcelize")
}

android {
    namespace = "com.score.callmetest"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.score.callmetest"
        minSdk = 21
        targetSdk = 35
        versionCode = 1
        versionName = "1.0.0"
        multiDexEnabled = true

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        //启用 rongpush，如不需要 RongPush 也可以禁用
        manifestPlaceholders["RONG_PUSH_ENABLE"] = "true"

        val timestamp = SimpleDateFormat("MMddHHmmss", Locale.getDefault()).apply {
            timeZone = TimeZone.getTimeZone("GMT+8:00")
        }.format(Date())


        setProperty("archivesBaseName", "Callme_v${versionName}-${versionCode}_${timestamp}")
        
        // Room 数据库模式导出位置配置
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += mapOf(
                    "room.schemaLocation" to "$projectDir/schemas",
                    "room.incremental" to "true",
                    "room.expandProjection" to "true"
                )
            }
        }
    }

    signingConfigs {
        create("test") {
            keyAlias = "callme-test"
            keyPassword = "test123"
            storeFile = file("callme-test.keystore")
            storePassword = "test123"
        }
    }

    buildTypes {
        debug {
//            buildConfigField("String","RONG_APP_KEY","\"n19jmcy5n8o69\"")

            isMinifyEnabled = false
            isShrinkResources = false // Debug 版本也不启用资源压缩，避免调试问题
            signingConfig = signingConfigs.getByName("test")

            // Debug 版本可以选择性启用 ProGuard 规则，但不混淆
            proguardFiles(
                getDefaultProguardFile("proguard-android.txt"),
                "proguard-rules.pro"
            )
        }

        release {
//            buildConfigField("String","RONG_APP_KEY","\"n19jmcy5n8o69\"")

            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = false // 确保 release 版本不可调试

            signingConfig = signingConfigs.getByName("test")

            // 使用优化版本的 ProGuard 配置
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }

    sourceSets {
        getByName("main") {
            assets.srcDirs("src/main/assets")
        }
        getByName("androidTest") {
            // 添加对 Room schema 文件的打包支持
            assets.srcDir("$projectDir/schemas")
        }
    }

    flavorDimensions += "environment"

    productFlavors {
        create("local") {
            dimension = "environment"
            applicationId = "com.score.callmetest"
            buildConfigField("int", "SERVER_TYPE", "2")
        }

        create("product") {
            dimension = "environment"
            applicationId = "com.score.callmetest"
            buildConfigField("int", "SERVER_TYPE", "1")
        }

        create("publish") {
            dimension = "environment"
            applicationId = "com.score.callme"
            buildConfigField("int", "SERVER_TYPE", "1")
        }
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)


    // Retrofit & 网络请求相关依赖
    implementation(libs.retrofit)
    implementation(libs.retrofit.kotlinx.serialization.converter)
    implementation(libs.okhttp)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.okhttp.logging.interceptor)

    // Glide 图片加载库
    implementation(libs.glide)
    implementation(libs.androidx.swiperefreshlayout)
    implementation(libs.cronet.embedded)
    annotationProcessor(libs.glide.compiler)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    implementation(libs.androidx.viewpager2)
    implementation(libs.material)

    implementation("com.github.chrisbanes:PhotoView:2.3.0")
    implementation("com.github.yyued:SVGAPlayer-Android:2.6.1")
    implementation("io.socket:socket.io-client:1.0.0") {
        exclude(group = "org.json", module = "json")
    }
    implementation("com.google.android.flexbox:flexbox:3.0.0")
    implementation("io.agora.rtc:full-sdk:4.5.2")
    implementation("com.google.code.gson:gson:2.11.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    
    // 圆形图片控件
    implementation("de.hdodenhof:circleimageview:3.1.0")
    // log
    implementation("com.jakewharton.timber:timber:5.0.1")
    
    // 融云SDK依赖 - 使用5.7.5稳定版本
    implementation("cn.rongcloud.sdk:im_lib:5.7.5") // UI组件
    implementation("cn.rongcloud.sdk.push:rong:5.7.5") // 推送插件

    // emoji
    implementation(libs.androidx.emoji2.emojipicker)

    // 数据库-room
    implementation(libs.room.runtime)
    kapt(libs.room.compiler)
    implementation(libs.room.ktx)
    implementation(libs.billing)
    implementation(libs.adjust.android)
    implementation(libs.adjust.android.webbridge)
    implementation(libs.installreferrer)
    implementation(libs.play.services.ads.identifier)

//    implementation("com.github.promeg:tinypinyin:2.0.3")

    implementation(libs.androidx.media3.exoplayer)
    implementation(libs.androidx.media3.ui)
    implementation(libs.androidx.fragment.ktx)
}